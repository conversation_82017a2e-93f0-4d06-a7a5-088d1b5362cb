// BandCalendarLib.Tests/Services/ReminderServiceTests.cs
using BandCalendarLib.Models;
using BandCalendarLib.Services;
using BandCalendarLib.Services.Internal;
using BandCalendarLib.Services.Util;
using Google.Apis.Calendar.v3.Data;
using Microsoft.Extensions.Logging.Abstractions;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using NSubstitute;
using NSubstitute.Core;
using Shouldly;

namespace BandCalendarLib.Tests.Services
{
    [TestClass]
    public class ReminderServiceTests
    {
        private ICalendarDataSource _calendarDataSource = null!;
        private ITravelTimeService _travelTimeService = null!;
        private GoogleApiConfig _apiConfig = null!;
        private IGigCalendarService _gigCalendarService = null!;
        private ReminderService _reminderService = null!;
        private IClock _clock = null!;

        [TestInitialize]
        public void Setup()
        {
            // Setup mocks
            _calendarDataSource = Substitute.For<ICalendarDataSource>();
            _travelTimeService = Substitute.For<ITravelTimeService>();
            _apiConfig = new GoogleApiConfig
            {
                GigCalendarId = "gig-calendar-id",
                ReminderCalendarId = "reminder-calendar-id",
                HomeAddress = "123 Test St"
            };
            _gigCalendarService = Substitute.For<IGigCalendarService>();

            // Setup mock for travel time service
            _travelTimeService.GetTravelTime(Arg.Any<string>(), Arg.Any<string>(), Arg.Any<DateTimeOffset>())
                .Returns(Task.FromResult(
                    new TravelTimeResult()
                    {
                        Success = true,
                        DurationInMinutes = 30,
                        DurationText = "30 mins"
                    }));

            _clock = Substitute.For<IClock>();
            _clock.Now.Returns(DateTimeOffset.UtcNow);
            _clock.Today.Returns(_ => _clock.Now.Date);

            // Create the service under test
            _reminderService = new ReminderService(
                _calendarDataSource,
                _travelTimeService,
                _apiConfig,
                _gigCalendarService,
                _clock,
                new NullLogger<ReminderService>());
        }

        [TestMethod]
        public async Task GetRemindersAsync_WithNoTypeFilter_ReturnsAllTypes()
        {
            // Arrange
            _clock.Now.Returns(new DateTimeOffset(2025, 1, 10, 0, 0, 0, TimeSpan.Zero));
            
            var startDate = new DateTimeOffset(2025, 1, 1, 0, 0, 0, TimeSpan.Zero);
            var endDate = new DateTimeOffset(2025, 1, 31, 0, 0, 0, TimeSpan.Zero);

            var gigEvents = new List<CalendarEvent>
            {
                new CalendarEvent
                {
                    Id = "gig1",
                    Summary = "Test Gig 1",
                    StartTime = new DateTimeOffset(2025, 1, 15, 20, 0, 0, TimeSpan.Zero),
                    EndTime = new DateTimeOffset(2025, 1, 15, 23, 0, 0, TimeSpan.Zero),
                    Location = "123 Main St"
                }
            };

            _gigCalendarService.GetGigEvents(Arg.Any<DateTimeOffset>(), Arg.Any<DateTimeOffset>())
                .Returns(Task.FromResult(gigEvents));

            _calendarDataSource.GetReminderCalendarEventsAsync(Arg.Any<ReminderType>(), Arg.Any<DateTimeOffset>(), Arg.Any<DateTimeOffset>())
                .Returns(Task.FromResult(new List<Event>()));

            // Mock travel time service
            _travelTimeService.GetTravelTime(Arg.Any<string>(), Arg.Any<string>(), Arg.Any<DateTimeOffset>())
                .Returns(Task.FromResult(
                    new TravelTimeResult()
                    {
                        Success = true,
                        DurationInMinutes = 30
                    }));

            // Act
            var result = await _reminderService.GetRemindersAsync(startDate, endDate);

            // Assert
            result.ShouldNotBeNull();
            result.Count.ShouldBe(2); // One charge reminder and one drive reminders
            result.ShouldContain(r => r.Type == ReminderType.ChargeEquipment);
            result.ShouldContain(r => r.Type == ReminderType.DriveToGig);
        }

        [TestMethod]
        public async Task GetRemindersAsync_GigsOnExtremesOfDateRange_IncludesCorrectReminders()
        {
            // Arrange
            _clock.Now.Returns(new DateTimeOffset(2024, 12, 10, 0, 0, 0, TimeSpan.Zero));

            var startDate = new DateTimeOffset(2025, 1, 1, 0, 0, 0, TimeSpan.Zero);
            var endDate = new DateTimeOffset(2025, 1, 31, 0, 0, 0, TimeSpan.Zero);

            var gigEvents = new List<CalendarEvent>
            {
                new CalendarEvent
                {
                    Id = "gig1",
                    Summary = "Gig on first of date range",
                    StartTime = startDate.AddHours(12),
                    EndTime = startDate.AddHours(13),
                    Location = "123 Main St"
                },
                new CalendarEvent
                {
                    Id = "gig2",
                    Summary = "Gig on last day of date range",
                    StartTime = endDate.AddHours(12),
                    EndTime = endDate.AddHours(13),
                    Location = "123 Main St"
                }
            };

            _gigCalendarService.GetGigEvents(Arg.Any<DateTimeOffset>(), Arg.Any<DateTimeOffset>())
                .Returns(Task.FromResult(gigEvents));

            _calendarDataSource.GetReminderCalendarEventsAsync(Arg.Any<ReminderType>(), Arg.Any<DateTimeOffset>(), Arg.Any<DateTimeOffset>())
                .Returns(Task.FromResult(new List<Event>()));

            // Mock travel time service
            _travelTimeService.GetTravelTime(Arg.Any<string>(), Arg.Any<string>(), Arg.Any<DateTimeOffset>())
                .Returns(Task.FromResult(
                    new TravelTimeResult()
                    {
                        Success = true,
                        DurationInMinutes = 30
                    }));

            // Act
            var result = await _reminderService.GetRemindersAsync(startDate, endDate);

            // Assert
            result.ShouldNotBeNull();
            result.Count.ShouldBe(4); // One charge and one drive reminder for each event
            result.ShouldContain(r => r.Type == ReminderType.ChargeEquipment);
            result.ShouldContain(r => r.Type == ReminderType.DriveToGig);
            result.SelectMany(r => r.RelatedGigIds).Distinct().ShouldBe(["gig1", "gig2"], ignoreOrder: true);
        }

        [TestMethod]
        public async Task GetRemindersAsync_WithTwoGigs_ReturnsOneChargeAndTwoDriveReminders()
        {
            // Arrange
            _clock.Now.Returns(new DateTimeOffset(2025, 1, 10, 0, 0, 0, TimeSpan.Zero));

            var startDate = new DateTimeOffset(2025, 1, 1, 0, 0, 0, TimeSpan.Zero);
            var endDate = new DateTimeOffset(2025, 1, 31, 0, 0, 0, TimeSpan.Zero);

            var gigEvents = new List<CalendarEvent>
            {
                new CalendarEvent
                {
                    Id = "gig1",
                    Summary = "Test Gig 1",
                    StartTime = new DateTimeOffset(2025, 1, 15, 12, 0, 0, TimeSpan.Zero),
                    EndTime = new DateTimeOffset(2025, 1, 15, 15, 0, 0, TimeSpan.Zero),
                    Location = "123 Main St"
                },
                new CalendarEvent
                {
                    Id = "gig2",
                    Summary = "Test Gig 2",
                    StartTime = new DateTimeOffset(2025, 1, 15, 18, 0, 0, TimeSpan.Zero),
                    EndTime = new DateTimeOffset(2025, 1, 15, 22, 0, 0, TimeSpan.Zero),
                    Location = "234 Main St"
                }
            };

            _gigCalendarService.GetGigEvents(Arg.Any<DateTimeOffset>(), Arg.Any<DateTimeOffset>())
                .Returns(Task.FromResult(gigEvents));

            _calendarDataSource.GetReminderCalendarEventsAsync(Arg.Any<ReminderType>(), Arg.Any<DateTimeOffset>(), Arg.Any<DateTimeOffset>())
                .Returns(Task.FromResult(new List<Event>()));

            _travelTimeService.GetTravelTime(Arg.Any<string>(), Arg.Any<string>(), Arg.Any<DateTimeOffset>())
                .Returns(Task.FromResult(
                    new TravelTimeResult()
                    {
                        Success = true,
                        DurationInMinutes = 30
                    }));

            // Act
            var result = await _reminderService.GetRemindersAsync(startDate, endDate);

            // Assert
            result.ShouldNotBeNull();
            result.Count.ShouldBe(3); // One charge reminder and two drive reminder
            result.Where(result => result.Type == ReminderType.ChargeEquipment).Count().ShouldBe(1);
            result.Where(result => result.Type == ReminderType.DriveToGig).Count().ShouldBe(2);
        }

        [TestMethod]
        public async Task GetRemindersAsync_WithChargeEquipmentFilter_ReturnsOnlyChargeReminders()
        {
            // Arrange
            _clock.Now.Returns(new DateTimeOffset(2025, 1, 10, 0, 0, 0, TimeSpan.Zero));

            var startDate = new DateTimeOffset(2025, 1, 1, 0, 0, 0, TimeSpan.Zero);
            var endDate = new DateTimeOffset(2025, 1, 31, 0, 0, 0, TimeSpan.Zero);

            var gigEvents = new List<CalendarEvent>
            {
                new CalendarEvent
                {
                    Id = "gig1",
                    Summary = "Test Gig 1",
                    StartTime = new DateTimeOffset(2025, 1, 15, 20, 0, 0, TimeSpan.Zero),
                    EndTime = new DateTimeOffset(2025, 1, 15, 23, 0, 0, TimeSpan.Zero)
                }
            };

            _gigCalendarService.GetGigEvents(Arg.Any<DateTimeOffset>(), Arg.Any<DateTimeOffset>())
                .Returns(Task.FromResult(gigEvents));

            _calendarDataSource.GetReminderCalendarEventsAsync(Arg.Any<ReminderType>(), Arg.Any<DateTimeOffset>(), Arg.Any<DateTimeOffset>())
                .Returns(Task.FromResult(new List<Event>()));

            // Act
            var result = await _reminderService.GetRemindersAsync(startDate, endDate, ReminderType.ChargeEquipment);

            // Assert
            result.ShouldNotBeNull();
            result.Count.ShouldBe(1);
            result[0].Type.ShouldBe(ReminderType.ChargeEquipment);
            result[0].AlreadyExists.ShouldBeFalse();
            result[0].IsOutdated.ShouldBeFalse();
            result[0].IsOld.ShouldBeFalse();
            result[0].RelatedGigIds.ShouldContain("gig1");
            result[0].Title.ShouldNotBeNullOrEmpty();
            result[0].Description.ShouldNotBeNullOrEmpty();
            // Timezones make these tricky
            //result[0].StartTime.ShouldBe(new DateTimeOffset(2025, 1, 15, 9, 0, 0, TimeSpan.Zero));
            //result[0].EndTime.ShouldBe(new DateTimeOffset(2025, 1, 15, 17, 0, 0, TimeSpan.Zero));
        }

        [DataTestMethod]
        [DataRow(ReminderType.ChargeEquipment)]
        [DataRow(ReminderType.DriveToGig)]
        public async Task GetRemindersAsync_WithExistingReminder_ReturnsExistingReminder(ReminderType reminderType)
        {
            _clock.Now.Returns(new DateTimeOffset(2025, 1, 10, 0, 0, 0, TimeSpan.Zero));

            // Arrange
            var startDate = new DateTimeOffset(2025, 1, 1, 0, 0, 0, TimeSpan.Zero);
            var endDate = new DateTimeOffset(2025, 1, 31, 0, 0, 0, TimeSpan.Zero);

            var gigEvents = new List<CalendarEvent>
            {
                new CalendarEvent
                {
                    Id = "gig1",
                    Summary = "Test Gig 1",
                    StartTime = new DateTimeOffset(2025, 1, 15, 20, 0, 0, TimeSpan.Zero),
                    EndTime = new DateTimeOffset(2025, 1, 15, 23, 0, 0, TimeSpan.Zero),
                    Location = "Test Venue, 123 Main St"
                }
            };

            var existingReminder = new Event
            {
                Id = "reminder1",
                Summary = $"{reminderType} for Test Gig 1",
                Start = new EventDateTime { DateTimeDateTimeOffset = new DateTimeOffset(2025, 1, 15, 18, 30, 0, TimeSpan.Zero) },
                End = new EventDateTime { DateTimeDateTimeOffset = new DateTimeOffset(2025, 1, 15, 19, 0, 0, TimeSpan.Zero) },
                UpdatedDateTimeOffset = DateTimeOffset.MinValue
            };
            existingReminder.SetReminderType(reminderType);
            existingReminder.SetReminderSourceEventId("gig1");

            _gigCalendarService.GetGigEvents(Arg.Any<DateTimeOffset>(), Arg.Any<DateTimeOffset>())
                .Returns(Task.FromResult(gigEvents));

            _calendarDataSource.GetReminderCalendarEventsAsync(reminderType, Arg.Any<DateTimeOffset>(), Arg.Any<DateTimeOffset>())
                .Returns(Task.FromResult(new List<Event> { existingReminder }));

            // Act
            var result = await _reminderService.GetRemindersAsync(startDate, endDate, reminderType);

            // Assert
            result.ShouldNotBeNull();
            result.Count.ShouldBe(1);
            result[0].Type.ShouldBe(reminderType);
            result[0].AlreadyExists.ShouldBeTrue();
            result[0].RelatedGigIds.ShouldContain("gig1");
            result[0].IsOld.ShouldBeFalse();
            result[0].IsOutdated.ShouldBeFalse();
        }

        [DataTestMethod]
        [DataRow(ReminderType.ChargeEquipment)]
        [DataRow(ReminderType.DriveToGig)]
        public async Task GetRemindersAsync_WithExistingOutdatedReminder_ReturnsExistingReminderMarkedIsOutdated(ReminderType reminderType)
        {
            _clock.Now.Returns(new DateTimeOffset(2025, 1, 10, 0, 0, 0, TimeSpan.Zero));

            // Arrange
            var startDate = new DateTimeOffset(2025, 1, 1, 0, 0, 0, TimeSpan.Zero);
            var endDate = new DateTimeOffset(2025, 1, 31, 0, 0, 0, TimeSpan.Zero);

            var gigEvents = new List<CalendarEvent>
            {
                new CalendarEvent
                {
                    Id = "gig1",
                    Summary = "Test Gig 1",
                    StartTime = new DateTimeOffset(2025, 1, 15, 20, 0, 0, TimeSpan.Zero),
                    EndTime = new DateTimeOffset(2025, 1, 15, 23, 0, 0, TimeSpan.Zero),
                    Location = "Test Venue, 123 Main St",
                    UpdatedDateTimeOffset = _clock.Now.AddDays(-3)
                }
            };

            var existingReminder = new Event
            {
                Id = "reminder1",
                Summary = $"{reminderType} for Test Gig 1",
                Start = new EventDateTime { DateTimeDateTimeOffset = new DateTimeOffset(2025, 1, 15, 18, 30, 0, TimeSpan.Zero) },
                End = new EventDateTime { DateTimeDateTimeOffset = new DateTimeOffset(2025, 1, 15, 19, 0, 0, TimeSpan.Zero) },
                UpdatedDateTimeOffset = _clock.Now.AddDays(-7)
            };
            existingReminder.SetReminderType(reminderType);
            existingReminder.SetReminderSourceEventId("gig1");

            _gigCalendarService.GetGigEvents(Arg.Any<DateTimeOffset>(), Arg.Any<DateTimeOffset>())
                .Returns(Task.FromResult(gigEvents));

            _calendarDataSource.GetReminderCalendarEventsAsync(reminderType, Arg.Any<DateTimeOffset>(), Arg.Any<DateTimeOffset>())
                .Returns(Task.FromResult(new List<Event> { existingReminder }));

            // Act
            var result = await _reminderService.GetRemindersAsync(startDate, endDate, reminderType);

            // Assert
            result.ShouldNotBeNull();
            result.Count.ShouldBe(1);
            result[0].Type.ShouldBe(reminderType);
            result[0].AlreadyExists.ShouldBeTrue();
            result[0].RelatedGigIds.ShouldContain("gig1");
            result[0].IsOld.ShouldBeFalse();
            result[0].IsOutdated.ShouldBeTrue();
        }

        [DataTestMethod]
        [DataRow(ReminderType.ChargeEquipment)]
        [DataRow(ReminderType.DriveToGig)]
        public async Task GetRemindersAsync_WithoutExistingReminder_ProducesNewReminder(ReminderType reminderType)
        {
            // Arrange
            _clock.Now.Returns(new DateTimeOffset(2025, 1, 10, 0, 0, 0, TimeSpan.Zero));

            var startDate = new DateTimeOffset(2025, 1, 1, 0, 0, 0, TimeSpan.Zero);
            var endDate = new DateTimeOffset(2025, 1, 31, 0, 0, 0, TimeSpan.Zero);

            var gigEvents = new List<CalendarEvent>
            {
                new CalendarEvent
                {
                    Id = "gig1",
                    Summary = "Test Gig 1",
                    StartTime = new DateTimeOffset(2025, 1, 15, 20, 0, 0, TimeSpan.Zero),
                    EndTime = new DateTimeOffset(2025, 1, 15, 23, 0, 0, TimeSpan.Zero),
                    Location = "Test Venue, 123 Main St"
                }
            };

            _gigCalendarService.GetGigEvents(Arg.Any<DateTimeOffset>(), Arg.Any<DateTimeOffset>())
                .Returns(Task.FromResult(gigEvents));

            _calendarDataSource.GetReminderCalendarEventsAsync(reminderType, Arg.Any<DateTimeOffset>(), Arg.Any<DateTimeOffset>())
                .Returns(Task.FromResult(new List<Event>()));

            // Act
            var result = await _reminderService.GetRemindersAsync(startDate, endDate, reminderType);

            // Assert
            result.ShouldNotBeNull();
            result.Count.ShouldBe(1);
            result[0].Type.ShouldBe(reminderType);
            result[0].AlreadyExists.ShouldBeFalse();
            result[0].RelatedGigIds.ShouldContain("gig1");
            result[0].IsOld.ShouldBeFalse();
            result[0].IsOutdated.ShouldBeFalse();
        }

        [DataTestMethod]
        [DataRow(ReminderType.ChargeEquipment)]
        [DataRow(ReminderType.DriveToGig)]
        public async Task GetRemindersAsync_WithExistingOldReminder_ReturnsExistingReminderMarkedAsIsOld(ReminderType reminderType)
        {
            // Arrange
            _clock.Now.Returns(new DateTimeOffset(2025, 1, 16, 0, 0, 0, TimeSpan.Zero));

            var startDate = new DateTimeOffset(2025, 1, 1, 0, 0, 0, TimeSpan.Zero);
            var endDate = new DateTimeOffset(2025, 1, 31, 0, 0, 0, TimeSpan.Zero);

            var gigEvents = new List<CalendarEvent>
            {
                new CalendarEvent
                {
                    Id = "gig1",
                    Summary = "Test Gig 1",
                    StartTime = new DateTimeOffset(2025, 1, 15, 20, 0, 0, TimeSpan.Zero),
                    EndTime = new DateTimeOffset(2025, 1, 15, 23, 0, 0, TimeSpan.Zero),
                    Location = "Test Venue, 123 Main St"
                }
            };

            var existingReminder = new Event
            {
                Id = "reminder1",
                Summary = $"{reminderType} for Test Gig 1",
                Start = new EventDateTime { DateTimeDateTimeOffset = new DateTimeOffset(2025, 1, 15, 18, 30, 0, TimeSpan.Zero) },
                End = new EventDateTime { DateTimeDateTimeOffset = new DateTimeOffset(2025, 1, 15, 19, 0, 0, TimeSpan.Zero) },
                UpdatedDateTimeOffset = DateTimeOffset.MinValue
            };
            existingReminder.SetReminderType(reminderType);
            existingReminder.SetReminderSourceEventId("gig1");

            _gigCalendarService.GetGigEvents(Arg.Any<DateTimeOffset>(), Arg.Any<DateTimeOffset>())
                .Returns(Task.FromResult(gigEvents));

            _calendarDataSource.GetReminderCalendarEventsAsync(reminderType, Arg.Any<DateTimeOffset>(), Arg.Any<DateTimeOffset>())
                .Returns(Task.FromResult(new List<Event> { existingReminder }));

            // Act
            var result = await _reminderService.GetRemindersAsync(startDate, endDate, reminderType);

            // Assert
            result.ShouldNotBeNull();
            result.Count.ShouldBe(1);
            result[0].Type.ShouldBe(reminderType);
            result[0].AlreadyExists.ShouldBeTrue();
            result[0].RelatedGigIds.ShouldContain("gig1");
            result[0].IsOld.ShouldBeTrue();
            result[0].IsOutdated.ShouldBeFalse();
        }

        [DataTestMethod]
        [DataRow(ReminderType.ChargeEquipment)]
        [DataRow(ReminderType.DriveToGig)]
        public async Task GetRemindersAsync_WithoutExistingReminderForPastEvent_DoesNotReturnNewReminders(ReminderType reminderType)
        {
            // Arrange
            _clock.Now.Returns(new DateTimeOffset(2025, 1, 20, 0, 0, 0, TimeSpan.Zero));

            var startDate = new DateTimeOffset(2025, 1, 1, 0, 0, 0, TimeSpan.Zero);
            var endDate = new DateTimeOffset(2025, 1, 31, 0, 0, 0, TimeSpan.Zero);

            var gigEvents = new List<CalendarEvent>
            {
                new CalendarEvent
                {
                    Id = "gig1",
                    Summary = "Test Gig 1",
                    StartTime = new DateTimeOffset(2025, 1, 15, 20, 0, 0, TimeSpan.Zero),
                    EndTime = new DateTimeOffset(2025, 1, 15, 23, 0, 0, TimeSpan.Zero),
                    Location = "Test Venue, 123 Main St"
                }
            };

            _gigCalendarService.GetGigEvents(Arg.Any<DateTimeOffset>(), Arg.Any<DateTimeOffset>())
                .Returns(Task.FromResult(gigEvents));

            _calendarDataSource.GetReminderCalendarEventsAsync(reminderType, Arg.Any<DateTimeOffset>(), Arg.Any<DateTimeOffset>())
                .Returns(Task.FromResult(new List<Event> { }));

            // Act
            var result = await _reminderService.GetRemindersAsync(startDate, endDate, reminderType);

            // Assert
            result.ShouldNotBeNull();
            result.Count.ShouldBe(0);
        }

        [TestMethod]
        public async Task CreateChargeEquipmentReminders_SetsExtendedProperties()
        {
            // Arrange
            _clock.Now.Returns(new DateTimeOffset(2025, 1, 10, 0, 0, 0, TimeSpan.Zero));
            var startDate = new DateTimeOffset(2025, 1, 1, 0, 0, 0, TimeSpan.Zero);
            var endDate = new DateTimeOffset(2025, 1, 31, 0, 0, 0, TimeSpan.Zero);

            var gigEvents = new List<CalendarEvent>
            {
                new CalendarEvent
                {
                    Id = "gig1",
                    Summary = "Test Gig 1",
                    StartTime = new DateTimeOffset(2025, 1, 15, 20, 0, 0, TimeSpan.Zero),
                    EndTime = new DateTimeOffset(2025, 1, 15, 23, 0, 0, TimeSpan.Zero)
                }
            };

            _gigCalendarService.GetGigEvents(Arg.Any<DateTimeOffset>(), Arg.Any<DateTimeOffset>())
                .Returns(Task.FromResult(gigEvents));

            // Capture the Event object passed to the CreateReminderCalendarEvent method
            Event capturedEvent = null!;

            // Setup mock for CreateReminderCalendarEvent

            _calendarDataSource.GetReminderCalendarEventsAsync(Arg.Any<ReminderType>(), Arg.Any<DateTimeOffset>(), Arg.Any<DateTimeOffset>())
                .Returns(Task.FromResult(new List<Event>()));

            _calendarDataSource.CreateReminderCalendarEventAsync(Arg.Do<Event>(e => capturedEvent = e))
                .Returns(Task.FromResult(new Event
                {
                    Id = "reminder1",
                    Summary = "Charge stuff",
                    Start = new EventDateTime { DateTimeDateTimeOffset = new DateTimeOffset(2025, 1, 15, 9, 0, 0, TimeSpan.Zero) },
                    End = new EventDateTime { DateTimeDateTimeOffset = new DateTimeOffset(2025, 1, 15, 17, 0, 0, TimeSpan.Zero) }
                }));

            // Act
            var result = await _reminderService.CreateReminderEventsAsync(startDate, endDate, ReminderType.ChargeEquipment);

            // Assert
            result.ShouldNotBeNull();
            result.Count.ShouldBe(1);
            result[0].Type.ShouldBe(ReminderType.ChargeEquipment);
            result[0].Created.ShouldBeTrue();

            // Verify extended properties were set
            capturedEvent.ShouldNotBeNull();
            capturedEvent.ExtendedProperties.ShouldNotBeNull();
            capturedEvent.ExtendedProperties.Shared.ShouldNotBeNull();
            capturedEvent.ExtendedProperties.Shared.ShouldContainKey("band-calendar:reminder-type");
            capturedEvent.ExtendedProperties.Shared["band-calendar:reminder-type"].ShouldBe(ReminderType.ChargeEquipment.ToString());
            capturedEvent.ExtendedProperties.Shared.ShouldContainKey("band-calendar:reminder-source-event-ids");
            capturedEvent.ExtendedProperties.Shared["band-calendar:reminder-source-event-ids"].ShouldBe("gig1");
        }

        [DataTestMethod]
        [DataRow(ReminderType.ChargeEquipment)]
        [DataRow(ReminderType.DriveToGig)]
        public async Task GetRemindersAsync_WithOrphanedReminder_ReturnsOrphanedReminderMarkedAsOutdated(ReminderType reminderType)
        {
            // Arrange
            _clock.Now.Returns(new DateTimeOffset(2025, 1, 10, 0, 0, 0, TimeSpan.Zero));

            var startDate = new DateTimeOffset(2025, 1, 1, 0, 0, 0, TimeSpan.Zero);
            var endDate = new DateTimeOffset(2025, 1, 31, 0, 0, 0, TimeSpan.Zero);

            // But there is an existing reminder that references a gig that no longer exists
            var orphanedReminder = new Event
            {
                Id = "reminder1",
                Summary = "Reminder for Deleted Gig",
                Start = new EventDateTime { DateTimeDateTimeOffset = new DateTimeOffset(2025, 1, 15, 18, 30, 0, TimeSpan.Zero) },
                End = new EventDateTime { DateTimeDateTimeOffset = new DateTimeOffset(2025, 1, 15, 19, 0, 0, TimeSpan.Zero) },
                UpdatedDateTimeOffset = DateTimeOffset.MinValue
            };
            orphanedReminder.SetReminderType(reminderType);
            orphanedReminder.SetReminderSourceEventId("deleted-gig-id");

            _gigCalendarService.GetGigEvents(Arg.Any<DateTimeOffset>(), Arg.Any<DateTimeOffset>())
                .Returns(Task.FromResult(new List<CalendarEvent>()));

            // Setup the mock to return the orphaned reminder
            _calendarDataSource.GetReminderCalendarEventsAsync(reminderType, Arg.Any<DateTimeOffset>(), Arg.Any<DateTimeOffset>())
                .Returns(Task.FromResult(new List<Event> { orphanedReminder }));

            // Act
            var result = await _reminderService.GetRemindersAsync(startDate, endDate, reminderType);

            // Assert
            result.ShouldNotBeNull();
            result.Count.ShouldBe(1);
            result[0].Type.ShouldBe(reminderType);
            result[0].AlreadyExists.ShouldBeTrue();
            result[0].RelatedGigIds.ShouldContain("deleted-gig-id");
            result[0].IsOld.ShouldBeFalse();
            result[0].IsOutdated.ShouldBeTrue();
            result[0].OutdatedReason.ShouldContain("All associated gig events have been deleted or moved outside the date range");
        }

        [DataTestMethod]
        [DataRow(ReminderType.ChargeEquipment)]
        [DataRow(ReminderType.DriveToGig)]
        public async Task GetRemindersAsync_WithOldOrphanedReminder_ReturnsOrphanedReminderMarkedAsOldAndOutdated(ReminderType reminderType)
        {
            // Arrange
            _clock.Now.Returns(new DateTimeOffset(2025, 1, 16, 0, 0, 0, TimeSpan.Zero));

            var startDate = new DateTimeOffset(2025, 1, 1, 0, 0, 0, TimeSpan.Zero);
            var endDate = new DateTimeOffset(2025, 1, 31, 0, 0, 0, TimeSpan.Zero);

            // No gigs in the current date range
            var gigEvents = new List<CalendarEvent>();

            // But there is an existing reminder that references a gig that no longer exists
            // and the reminder is for a date in the past
            var orphanedReminder = new Event
            {
                Id = "reminder1",
                Summary = "Charge Equipment for Deleted Gig",
                Start = new EventDateTime { DateTimeDateTimeOffset = new DateTimeOffset(2025, 1, 15, 18, 30, 0, TimeSpan.Zero) },
                End = new EventDateTime { DateTimeDateTimeOffset = new DateTimeOffset(2025, 1, 15, 19, 0, 0, TimeSpan.Zero) },
                UpdatedDateTimeOffset = DateTimeOffset.MinValue
            };
            orphanedReminder.SetReminderType(reminderType);
            orphanedReminder.SetReminderSourceEventId("deleted-gig-id");

            _gigCalendarService.GetGigEvents(Arg.Any<DateTimeOffset>(), Arg.Any<DateTimeOffset>())
                .Returns(Task.FromResult(gigEvents));

            // Setup the mock to return the orphaned reminder
            _calendarDataSource.GetReminderCalendarEventsAsync(reminderType, Arg.Any<DateTimeOffset>(), Arg.Any<DateTimeOffset>())
                .Returns(Task.FromResult(new List<Event> { orphanedReminder }));

            // Act
            var result = await _reminderService.GetRemindersAsync(startDate, endDate, reminderType);

            // Assert
            result.ShouldNotBeNull();
            result.Count.ShouldBe(1);
            result[0].Type.ShouldBe(reminderType);
            result[0].AlreadyExists.ShouldBeTrue();
            result[0].RelatedGigIds.ShouldContain("deleted-gig-id");
            result[0].IsOld.ShouldBeTrue();
            result[0].IsOutdated.ShouldBeTrue();
            result[0].OutdatedReason.ShouldContain("All associated gig events have been deleted or moved outside the date range");
        }

        [TestMethod]
        public async Task GetRemindersAsync_DriveRemindersWithTwoGigsBackToBack_SecondReminderIsFromFirstGigLocation()
        {
            // Arrange
            _clock.Now.Returns(new DateTimeOffset(2025, 1, 10, 0, 0, 0, TimeSpan.Zero));

            var startDate = new DateTimeOffset(2025, 1, 1, 0, 0, 0, TimeSpan.Zero);
            var endDate = new DateTimeOffset(2025, 1, 31, 0, 0, 0, TimeSpan.Zero);

            var gigEvents = new List<CalendarEvent>
            {
                new CalendarEvent
                {
                    Id = "gig1",
                    Summary = "Test Gig 1",
                    StartTime = new DateTimeOffset(2025, 1, 15, 12, 0, 0, TimeSpan.Zero),
                    EndTime = new DateTimeOffset(2025, 1, 15, 15, 0, 0, TimeSpan.Zero),
                    Location = "123 Main St"
                },
                new CalendarEvent
                {
                    Id = "gig2",
                    Summary = "Test Gig 2",
                    StartTime = new DateTimeOffset(2025, 1, 15, 18, 0, 0, TimeSpan.Zero),
                    EndTime = new DateTimeOffset(2025, 1, 15, 22, 0, 0, TimeSpan.Zero),
                    Location = "234 Main St"
                }
            };

            _gigCalendarService.GetGigEvents(Arg.Any<DateTimeOffset>(), Arg.Any<DateTimeOffset>())
                .Returns(Task.FromResult(gigEvents));

            _calendarDataSource.GetReminderCalendarEventsAsync(Arg.Any<ReminderType>(), Arg.Any<DateTimeOffset>(), Arg.Any<DateTimeOffset>())
                .Returns(Task.FromResult(new List<Event>()));

            _travelTimeService.GetTravelTime(Arg.Any<string>(), Arg.Any<string>(), Arg.Any<DateTimeOffset>())
                .Returns(Task.FromResult(
                    new TravelTimeResult()
                    {
                        Success = true,
                        DurationInMinutes = 30
                    }));

            // Act
            var result = await _reminderService.GetRemindersAsync(startDate, endDate);

            // Assert
            result.ShouldNotBeNull();
            result.Count.ShouldBe(3); // One charge reminder and two drive reminder
            result.Where(result => result.Type == ReminderType.ChargeEquipment).Count().ShouldBe(1);
            result.Where(result => result.Type == ReminderType.DriveToGig).Count().ShouldBe(2);
        }

        [TestMethod]
        public async Task CreateDriveToGigReminders_SetsExtendedProperties()
        {
            // Arrange
            _clock.Now.Returns(new DateTimeOffset(2025, 1, 10, 0, 0, 0, TimeSpan.Zero));

            var startDate = new DateTimeOffset(2025, 1, 1, 0, 0, 0, TimeSpan.Zero);
            var endDate = new DateTimeOffset(2025, 1, 31, 0, 0, 0, TimeSpan.Zero);

            var gigEvents = new List<CalendarEvent>
            {
                new CalendarEvent
                {
                    Id = "gig1",
                    Summary = "Test Gig 1",
                    StartTime = new DateTimeOffset(2025, 1, 15, 20, 0, 0, TimeSpan.Zero),
                    EndTime = new DateTimeOffset(2025, 1, 15, 23, 0, 0, TimeSpan.Zero),
                    Location = "Test Venue, 123 Main St"
                }
            };

            _gigCalendarService.GetGigEvents(Arg.Any<DateTimeOffset>(), Arg.Any<DateTimeOffset>())
                .Returns(Task.FromResult(gigEvents));

            _travelTimeService.GetTravelTime(Arg.Any<string>(), Arg.Any<string>(), Arg.Any<DateTimeOffset>())
                .Returns(Task.FromResult(
                    new TravelTimeResult()
                    {
                        Success = true,
                        DurationInMinutes = 30
                    }));

            // Capture the Event object passed to the CreateReminderCalendarEvent method
            Event capturedEvent = null!;

            // Setup mock for FindExistingLeaveForGigReminder
            _calendarDataSource.GetReminderCalendarEventsAsync(Arg.Any<ReminderType>(), Arg.Any<DateTimeOffset>(), Arg.Any<DateTimeOffset>())
                .Returns(Task.FromResult(new List<Event>()));

            // Setup mock for CreateReminderCalendarEvent
            _calendarDataSource.CreateReminderCalendarEventAsync(Arg.Do<Event>(e => capturedEvent = e))
                .Returns(Task.FromResult(new Event
                {
                    Id = "reminder1",
                    Summary = "Drive to Test Gig 1",
                    Start = new EventDateTime { DateTimeDateTimeOffset = new DateTimeOffset(2025, 1, 15, 19, 0, 0, TimeSpan.Zero) },
                    End = new EventDateTime { DateTimeDateTimeOffset = new DateTimeOffset(2025, 1, 15, 19, 30, 0, TimeSpan.Zero) }
                }));

            // Act
            var result = await _reminderService.CreateReminderEventsAsync(startDate, endDate, ReminderType.DriveToGig);

            // Assert
            result.ShouldNotBeNull();
            result.Count.ShouldBe(1);
            result[0].Type.ShouldBe(ReminderType.DriveToGig);
            result[0].Created.ShouldBeTrue();

            // Verify extended properties were set
            capturedEvent.ShouldNotBeNull();
            capturedEvent.ExtendedProperties.ShouldNotBeNull();
            capturedEvent.ExtendedProperties.Shared.ShouldNotBeNull();
            capturedEvent.ExtendedProperties.Shared.ShouldContainKey("band-calendar:reminder-type");
            capturedEvent.ExtendedProperties.Shared["band-calendar:reminder-type"].ShouldBe(ReminderType.DriveToGig.ToString());
            capturedEvent.ExtendedProperties.Shared.ShouldContainKey("band-calendar:reminder-source-event-ids");
            capturedEvent.ExtendedProperties.Shared["band-calendar:reminder-source-event-ids"].ShouldBe("gig1");
        }

        [TestMethod]
        public async Task CreateReminderEventsAsync_GigsOnExtremesOfDateRange_IncludesCorrectReminders()
        {
            // Arrange
            _clock.Now.Returns(new DateTimeOffset(2024, 12, 10, 0, 0, 0, TimeSpan.Zero));

            var startDate = new DateTimeOffset(2025, 1, 1, 0, 0, 0, TimeSpan.Zero);
            var endDate = new DateTimeOffset(2025, 1, 31, 0, 0, 0, TimeSpan.Zero);

            var gigEvents = new List<CalendarEvent>
            {
                new CalendarEvent
                {
                    Id = "gig1",
                    Summary = "Gig on first of date range",
                    StartTime = startDate.AddHours(12),
                    EndTime = startDate.AddHours(13),
                    Location = "123 Main St"
                },
                new CalendarEvent
                {
                    Id = "gig2",
                    Summary = "Gig on last day of date range",
                    StartTime = endDate.AddHours(12),
                    EndTime = endDate.AddHours(13),
                    Location = "123 Main St"
                }
            };

            _gigCalendarService.GetGigEvents(Arg.Any<DateTimeOffset>(), Arg.Any<DateTimeOffset>())
                .Returns(Task.FromResult(gigEvents));

            _calendarDataSource.GetReminderCalendarEventsAsync(Arg.Any<ReminderType>(), Arg.Any<DateTimeOffset>(), Arg.Any<DateTimeOffset>())
                .Returns(Task.FromResult(new List<Event>()));

            // Mock travel time service
            _travelTimeService.GetTravelTime(Arg.Any<string>(), Arg.Any<string>(), Arg.Any<DateTimeOffset>())
                .Returns(Task.FromResult(
                    new TravelTimeResult()
                    {
                        Success = true,
                        DurationInMinutes = 30
                    }));

            // Act
            var result = await _reminderService.CreateReminderEventsAsync(startDate, endDate);

            // Assert
            result.ShouldNotBeNull();
            result.Count.ShouldBe(4); // One charge and one drive reminder for each event
            result.ShouldContain(r => r.Type == ReminderType.ChargeEquipment);
            result.ShouldContain(r => r.Type == ReminderType.DriveToGig);
            result.SelectMany(r => r.RelatedGigIds).Distinct().ShouldBe(["gig1", "gig2"], ignoreOrder: true);
        }

        [TestMethod]
        public async Task DeleteReminder_WithValidId_ReturnsTrue()
        {
            // Arrange
            var reminderId = "reminder1";

            _calendarDataSource.DeleteReminderCalendarEventAsync(reminderId).Returns(Task.FromResult(true));

            // Act
            var result = await _reminderService.DeleteReminderEventAsync(reminderId);

            // Assert
            result.ShouldBeTrue();
            await _calendarDataSource.Received(1).DeleteReminderCalendarEventAsync(reminderId);
        }

        [TestMethod]
        public async Task DeleteReminder_WithEmptyId_ReturnsFalse()
        {
            // Arrange
            var reminderId = "";

            // Act
            var result = await _reminderService.DeleteReminderEventAsync(reminderId);

            // Assert
            result.ShouldBeFalse();
            await _calendarDataSource.DidNotReceive().DeleteReminderCalendarEventAsync(Arg.Any<string>());
        }
    }
}


