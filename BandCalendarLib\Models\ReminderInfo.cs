namespace BandCalendarLib.Models
{
    public class ReminderInfo
    {
        public string ReminderId { get; set; } = string.Empty;

        public ReminderType Type { get; set; } 

        public DateTimeOffset Date { get; set; }
        public DateTimeOffset StartTime { get; set; } 
        public DateTimeOffset EndTime { get; set; } 
        public List<int> AlertMinutes = new List<int>();

        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Location { get; set; } = string.Empty;

        public List<string> RelatedGigIds { get; set; } = [];

        public bool AlreadyExists { get; set; }
        public bool Created { get; set; }
        public bool IsOutdated { get; set; }
        public string OutdatedReason { get; set; } = string.Empty;
        public bool IsOld { get; set; }
    }
}
