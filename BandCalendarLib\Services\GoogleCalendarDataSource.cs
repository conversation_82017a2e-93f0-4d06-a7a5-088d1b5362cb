// BandCalendarLib/Services/GoogleCalendarDataSource.cs
using BandCalendarLib.Models;
using BandCalendarLib.Services.Internal;
using BandCalendarLib.Services.Util;
using Google.Apis.Calendar.v3;
using Google.Apis.Calendar.v3.Data;

namespace BandCalendarLib.Services
{
    /// <summary>
    /// Implementation of ICalendarDataSource that uses Google Calendar API
    /// </summary>
    public class GoogleCalendarDataSource : ICalendarDataSource
    {
        private readonly CalendarService _service;
        private readonly GoogleApiConfig _apiConfig;

        public GoogleCalendarDataSource(CalendarService service, GoogleApiConfig apiConfig)
        {
            _service = service ?? throw new ArgumentNullException(nameof(service));
            _apiConfig = apiConfig ?? throw new ArgumentNullException(nameof(apiConfig));

            ValidateConfig();
        }

        private void ValidateConfig()
        {
            if (string.IsNullOrEmpty(_apiConfig.GigCalendarId))
                throw new InvalidOperationException("Gig Calendar ID is not set in configuration");

            if (string.IsNullOrEmpty(_apiConfig.ReminderCalendarId))
                throw new InvalidOperationException("Reminder Calendar ID is not set in configuration");
        }

        /// <inheritdoc/>
        public async Task<List<Event>> GetGigCalendarEventsAsync(DateTimeOffset startDate, DateTimeOffset endDate)
        {
            // Create the request
            var request = _service.Events.List(_apiConfig.GigCalendarId);

            // Ensure dates are in Eastern Time
            var easternStartDate = TimeZoneHelper.ToEasternTime(startDate);
            var easternEndDate = TimeZoneHelper.ToEasternTime(endDate);

            request.TimeMinDateTimeOffset = easternStartDate;
            request.TimeMaxDateTimeOffset = easternEndDate.AddDays(1);
            request.ShowDeleted = false;
            request.SingleEvents = true;
            request.OrderBy = EventsResource.ListRequest.OrderByEnum.StartTime;

            // Get events
            var events = await request.ExecuteAsync();

            return events.Items?.ToList() ?? new List<Event>();
        }

        public async Task<List<Event>> GetReminderCalendarEventsAsync(ReminderType reminderType, DateTimeOffset startDate, DateTimeOffset endDate)
        {
            var request = _service.Events.List(_apiConfig.ReminderCalendarId);

            // Ensure dates are in Eastern Time
            var easternStartDate = TimeZoneHelper.ToEasternTime(startDate);
            var easternEndDate = TimeZoneHelper.ToEasternTime(endDate);

            request.TimeMinDateTimeOffset = easternStartDate;
            request.TimeMaxDateTimeOffset = easternEndDate.AddDays(1);
            request.ShowDeleted = false;
            request.SingleEvents = true;
            request.OrderBy = EventsResource.ListRequest.OrderByEnum.StartTime;
            request.SharedExtendedProperty = $"{ReminderEventExtendedProperty.ReminderType}={reminderType}";

            var events = await request.ExecuteAsync();

            return events.Items?.ToList() ?? new List<Event>();
        }

        /// <inheritdoc/>
        public async Task<Event> CreateReminderCalendarEventAsync(Event eventData)
        {
            return await _service.Events.Insert(eventData, _apiConfig.ReminderCalendarId).ExecuteAsync();
        }

        /// <inheritdoc/>
        public async Task<bool> DeleteReminderCalendarEventAsync(string eventId)
        {
            try
            {
                await _service.Events.Delete(_apiConfig.ReminderCalendarId, eventId).ExecuteAsync();
                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        /// <inheritdoc/>
        public async Task<Dictionary<string, bool>> BatchDeleteReminderCalendarEventsAsync(List<string> eventIds)
        {
            var results = new Dictionary<string, bool>();

            if (eventIds == null || !eventIds.Any())
            {
                return results;
            }

            try
            {
                // Create a batch request
                var batchRequest = new Google.Apis.Requests.BatchRequest(_service);

                // Add each delete request to the batch
                foreach (var eventId in eventIds)
                {
                    if (string.IsNullOrWhiteSpace(eventId))
                    {
                        results[eventId] = false;
                        continue;
                    }

                    var deleteRequest = _service.Events.Delete(_apiConfig.ReminderCalendarId, eventId);

                    // Add the request to the batch with a callback
                    batchRequest.Queue<string>(
                        deleteRequest,
                        (content, error, i, message) =>
                        {
                            // Store the result in the dictionary
                            results[eventId] = error == null;
                        });
                }

                // Execute the batch request if there are any requests queued
                if (batchRequest.Count > 0)
                {
                    await batchRequest.ExecuteAsync();
                }

                // If any eventIds weren't processed (due to being null/empty), mark them as failed
                foreach (var eventId in eventIds.Where(id => !results.ContainsKey(id)))
                {
                    results[eventId] = false;
                }

                return results;
            }
            catch (Exception)
            {
                // If the batch request fails, mark all as failed
                foreach (var eventId in eventIds)
                {
                    results[eventId] = false;
                }
                return results;
            }
        }
    }
}
