﻿using BandCalendarLib.Models;

namespace BandCalendarLib.Services
{
    public interface IReminderService
    {
        Task<List<ReminderInfo>> CreateReminderEventsAsync(DateTimeOffset startDate, DateTimeOffset endDate, ReminderType? type = null);
        Task<bool> DeleteReminderEventAsync(string reminderId);
        Task<Dictionary<string, bool>> DeleteReminderEventsAsync(List<string> reminderIds);
        Task<List<ReminderInfo>> GetRemindersAsync(DateTimeOffset startDate, DateTimeOffset endDate, ReminderType? type = null);
    }
}