using System;

namespace BandCalendarLib.Services.Util
{
    /// <summary>
    /// Helper class for timezone operations
    /// </summary>
    public static class TimeZoneHelper
    {
        public static TimeZoneInfo EasternTimeZone =>
            TimeZoneInfo.FindSystemTimeZoneById("Eastern Standard Time");

        /// <summary>
        /// Creates a DateTimeOffset in Eastern Time from a string
        /// </summary>
        /// <param name="dateString">The date string to parse</param>
        /// <returns>A DateTimeOffset in Eastern Time</returns>
        public static DateTimeOffset ParseAllDayDateToEasternTimeZone(string dateString)
        {
            var dateTime = DateTime.ParseExact(dateString, "yyyy-MM-dd", null);
            return new DateTimeOffset(dateTime, EasternTimeZone.GetUtcOffset(dateTime));
        }

        /// <summary>
        /// Converts a DateTimeOffset to Eastern Time
        /// </summary>
        /// <param name="dateTimeOffset">The DateTimeOffset to convert</param>
        /// <returns>A DateTimeOffset in Eastern Time</returns>
        public static DateTimeOffset ToEasternTime(DateTimeOffset dateTimeOffset)
        {
            return TimeZoneInfo.ConvertTime(dateTimeOffset, EasternTimeZone);
        }

        public static DateTimeOffset ForceEastern(DateTime dateTime)
        {
            return new DateTimeOffset(dateTime, EasternTimeZone.GetUtcOffset(dateTime));
        }

        /// <summary>
        /// Converts a UTC DateTimeOffset to Eastern Time
        /// </summary>
        /// <param name="utcDateTimeOffset">The UTC DateTimeOffset to convert</param>
        /// <returns>A DateTimeOffset in Eastern Time</returns>
        public static DateTimeOffset UtcToEasternTime(DateTimeOffset utcDateTimeOffset)
        {
            if (utcDateTimeOffset.Offset != TimeSpan.Zero)
            {
                throw new Exception("Attempted to convert from non-UTC time");
            }
            return TimeZoneInfo.ConvertTime(utcDateTimeOffset, EasternTimeZone);
        }
    }
}