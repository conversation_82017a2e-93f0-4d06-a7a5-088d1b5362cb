﻿using BandCalendarLib.Models;
using BandCalendarLib.Services.Internal;
using BandCalendarLib.Services.Util;
using Google.Apis.Calendar.v3.Data;
using Microsoft.Extensions.Logging;

namespace BandCalendarLib.Services
{
    public class ReminderService : IReminderService
    {
        private readonly ICalendarDataSource _calendarDataSource;
        private readonly ITravelTimeService _travelTimeService;
        private readonly GoogleApiConfig _apiConfig;
        private readonly IGigCalendarService _gigCalendarService;
        private readonly IClock _clock;
        private readonly ILogger<ReminderService> _logger;

        public ReminderService(
            ICalendarDataSource calendarDataSource,
            ITravelTimeService travelTimeService,
            GoogleApiConfig apiConfig,
            IGigCalendarService gigCalendarService,
            IClock clock,
            ILogger<ReminderService> logger)
        {
            _calendarDataSource = calendarDataSource ?? throw new ArgumentNullException(nameof(calendarDataSource));
            _travelTimeService = travelTimeService ?? throw new ArgumentNullException(nameof(travelTimeService));
            _apiConfig = apiConfig ?? throw new ArgumentNullException(nameof(apiConfig));
            _gigCalendarService = gigCalendarService ?? throw new ArgumentNullException(nameof(gigCalendarService));
            _clock = clock;
            _logger = logger;
            ValidateConfig();
        }

        public async Task<List<ReminderInfo>> GetRemindersAsync(DateTimeOffset startDate, DateTimeOffset endDate, ReminderType? type = null)
        {
            var gigs = await _gigCalendarService.GetGigEvents(startDate, endDate);

            var result = new List<ReminderInfo>();

            // If type is specified or null (all types), include charge equipment reminders
            if (type == null || type == ReminderType.ChargeEquipment)
            {
                var chargeReminders = await GetChargeEquipmentReminders(gigs, startDate, endDate);
                result.AddRange(chargeReminders);
            }

            // If type is specified or null (all types), include drive to gig reminders
            if (type == null || type == ReminderType.DriveToGig)
            {
                var driveReminders = await GetDriveToGigReminders(gigs, startDate, endDate);
                result.AddRange(driveReminders);
            }

            return result;
        }

        public async Task<List<ReminderInfo>> CreateReminderEventsAsync(DateTimeOffset startDate, DateTimeOffset endDate, ReminderType? type = null)
        {
            var reminders = await GetRemindersAsync(startDate, endDate, type);
            return await CreateCalendarEventsAsync(reminders);
        }

        public async Task<bool> DeleteReminderEventAsync(string reminderId)
        {
            try
            {
                ArgumentException.ThrowIfNullOrWhiteSpace(reminderId, nameof(reminderId));
                return await _calendarDataSource.DeleteReminderCalendarEventAsync(reminderId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting reminder");
                return false;
            }
        }

        public async Task<Dictionary<string, bool>> DeleteReminderEventsAsync(List<string> reminderIds)
        {
            if (reminderIds == null || !reminderIds.Any())
            {
                return new Dictionary<string, bool>();
            }

            try
            {
                _logger.LogInformation($"Deleting {reminderIds.Count} reminders in batch operation");
                var results = await _calendarDataSource.BatchDeleteReminderCalendarEventsAsync(reminderIds);

                var successCount = results.Values.Count(v => v);
                _logger.LogInformation($"Successfully deleted {successCount} out of {reminderIds.Count} reminders");

                return results;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in batch deletion of reminders");
                return reminderIds.ToDictionary(id => id, id => false);
            }
        }

        private void ValidateConfig()
        {
            if (string.IsNullOrEmpty(_apiConfig.ReminderCalendarId))
                throw new InvalidOperationException("Reminder Calendar ID is not set in configuration");
        }

        private async Task<List<ReminderInfo>> GetChargeEquipmentReminders(List<CalendarEvent> gigs, DateTimeOffset startDate, DateTimeOffset endDate)
        {
            var gigsGroupedByDate = gigs.GroupBy(g => g.StartTime.Date)
                .OrderBy(g => g.Key)
                .Select(g => (g.Key, g.ToList()))
                .ToList();

            var existingReminders = await _calendarDataSource.GetReminderCalendarEventsAsync(ReminderType.ChargeEquipment, startDate, endDate);

            // Create a set of dates for which we have gigs
            var gigDates = new HashSet<DateTime>(gigsGroupedByDate.Select(g => g.Key.Date));

            // Create a dictionary to track which reminders we've processed
            var processedReminderIds = new HashSet<string>();

            var result = new List<ReminderInfo>();

            // First, process reminders for dates with gigs
            foreach (var (date, gigsOnDate) in gigsGroupedByDate)
            {
                var existingReminder = existingReminders.Where(r => r.Start.DateTimeDateTimeOffset?.DateTime.Date == date.Date)
                    .FirstOrDefault();

                if (existingReminder != null)
                {
                    result.Add(MapCalendarEventToReminderInfo(existingReminder, gigsOnDate));
                    processedReminderIds.Add(existingReminder.Id);
                }
                else if (date >= _clock.Today.Date)
                {
                    // Use Eastern Time for reminder times
                    var firstGigOfDay = gigsOnDate.FirstOrDefault();

                    var easternDate = TimeZoneHelper.ForceEastern(date.Date);
                    var reminderStartTime = easternDate.AddHours(9);
                    var reminderEndTime = firstGigOfDay?.StartTime ?? reminderStartTime.AddHours(8);

                    if (reminderEndTime <= reminderStartTime)
                    {
                        reminderEndTime = reminderStartTime.AddHours(1);
                    }

                    result.Add(new ReminderInfo
                    {
                        Type = ReminderType.ChargeEquipment,
                        Date = date,
                        StartTime = reminderStartTime,
                        EndTime = reminderEndTime,
                        Title = "⚡ Charge stuff",
                        AlreadyExists = false,
                        Description = "Remember to charge equipment for upcoming gig",
                        RelatedGigIds = gigsOnDate.Select(g => g.Id).ToList(),
                        AlertMinutes = [0]
                    });
                }
                else
                {
                    _logger.LogInformation("Skipping charge reminder for date {date} because it is in the past.", date);
                }
            }

            // Now, process orphaned reminders (reminders that exist but don't match any gig dates)
            foreach (var reminder in existingReminders)
            {
                // Skip reminders we've already processed
                if (processedReminderIds.Contains(reminder.Id))
                {
                    continue;
                }

                // Get the date of the reminder
                var reminderDate = reminder.Start.DateTimeDateTimeOffset?.DateTime.Date;
                if (reminderDate == null)
                {
                    continue;
                }

                // If this reminder is for a date that doesn't have any gigs, it's orphaned
                if (!gigDates.Contains(reminderDate.Value))
                {
                    // Map the reminder to a ReminderInfo object with an empty list of gigs
                    var reminderInfo = MapCalendarEventToReminderInfo(reminder, []);
                    result.Add(reminderInfo);
                }
            }

            return result;
        }

        private async Task<List<ReminderInfo>> GetDriveToGigReminders(List<CalendarEvent> gigs, DateTimeOffset startDate, DateTimeOffset endDate)
        {
            var existingReminders = await _calendarDataSource.GetReminderCalendarEventsAsync(ReminderType.DriveToGig, startDate, endDate);

            // Create a set to track which reminders we've processed
            var processedReminderIds = new HashSet<string>();

            // Create a set of gig IDs for quick lookup
            var gigIds = new HashSet<string>(gigs.Select(g => g.Id));

            var result = new List<ReminderInfo>();

            // First, process reminders for existing gigs
            foreach (var gig in gigs)
            {
                // Try to find an existing reminder for this gig
                var existingReminder = existingReminders.FirstOrDefault(r => r.GetReminderSourceEventIds().Contains(gig.Id));

                if (existingReminder != null)
                {
                    var reminderInfo = MapCalendarEventToReminderInfo(existingReminder, [gig]);
                    result.Add(reminderInfo);
                    processedReminderIds.Add(existingReminder.Id);
                }
                else if (gig.StartTime < _clock.Today)
                {
                    _logger.LogInformation("Skipping drive reminder for gig {gigId} because it is in the past.", gig.Id);
                }
                else if (gig.IsAllDay)
                {
                    _logger.LogInformation("Gig is missing start time. Skipping drive reminder.");
                }
                else if (string.IsNullOrEmpty(gig.Location))
                {
                    _logger.LogInformation("Gig is missing location. Skipping drive reminder.");
                }
                else
                {
                    var arrivalTime = gig.StartTime.AddHours(-1);
                    var travelTimeResult = await _travelTimeService.GetTravelTime(_apiConfig.HomeAddress, gig.Location, arrivalTime);

                    if (!travelTimeResult.Success)
                    {
                        _logger.LogError("Travel time request failed");
                        continue;
                    }

                    var leaveTime = arrivalTime.AddMinutes(-travelTimeResult.DurationInMinutes);

                    result.Add(new ReminderInfo
                    {
                        Date = leaveTime.Date,
                        StartTime = leaveTime,
                        EndTime = arrivalTime,
                        Title = $"🚗 Drive to {gig.Summary}",
                        Description = $"Drive to gig ({travelTimeResult.DurationText} driving time). Arrive 1 hour before gig start at {gig.StartTime:h:mm tt}.",
                        Location = string.Empty,
                        AlreadyExists = false,
                        RelatedGigIds = [gig.Id],
                        IsOutdated = false,
                        Type = ReminderType.DriveToGig,
                        AlertMinutes = [60, 15]
                    });
                }
            }

            // Now, process orphaned reminders (reminders that exist but don't match any current gigs)
            foreach (var reminder in existingReminders)
            {
                // Skip reminders we've already processed
                if (processedReminderIds.Contains(reminder.Id))
                {
                    continue;
                }

                // Get the related gig IDs for this reminder
                var relatedGigIds = reminder.GetReminderSourceEventIds();

                // Check if any of the related gigs still exist in the current gig list
                var hasMatchingGigs = relatedGigIds.Any(gigIds.Contains);

                // If there are no matching gigs, this is an orphaned reminder
                if (!hasMatchingGigs)
                {
                    // Map the reminder to a ReminderInfo object with an empty list of gigs
                    var reminderInfo = MapCalendarEventToReminderInfo(reminder, []);
                    result.Add(reminderInfo);
                }
            }

            return result;
        }

        private async Task<List<ReminderInfo>> CreateCalendarEventsAsync(List<ReminderInfo> reminders)
        {
            var result = new List<ReminderInfo>();

            foreach (var reminderInfo in reminders)
            {
                if (reminderInfo.AlreadyExists)
                {
                    result.Add(reminderInfo);
                    continue;
                }

                var calendarEvent = MapReminderInfoToCalendarEvent(reminderInfo);
                var createdEvent = await _calendarDataSource.CreateReminderCalendarEventAsync(calendarEvent);

                reminderInfo.Created = true;
                reminderInfo.ReminderId = createdEvent.Id;
                result.Add(reminderInfo);
            }

            return result;
        }

        private static Event MapReminderInfoToCalendarEvent(ReminderInfo reminderInfo)
        {
            var calendarEvent = new Event
            {
                Summary = reminderInfo.Title,
                Description = reminderInfo.Description,
                Start = new EventDateTime
                {
                    DateTimeDateTimeOffset = reminderInfo.StartTime,
                },
                End = new EventDateTime
                {
                    DateTimeDateTimeOffset = reminderInfo.EndTime,
                },
                Reminders = new Event.RemindersData
                {
                    UseDefault = false,
                    Overrides = reminderInfo.AlertMinutes.Select(minutes => new EventReminder { Method = "popup", Minutes = minutes }).ToList()
                }
            };

            calendarEvent.SetReminderType(reminderInfo.Type);
            calendarEvent.SetReminderSourceEventIds(reminderInfo.RelatedGigIds);
            return calendarEvent;
        }

        private bool IsConformingReminderEvent(Event reminderEvent, out string reason)
        {
            var isConforming = true;
            reason = String.Empty;

            if (!reminderEvent.UpdatedDateTimeOffset.HasValue)
            {
                isConforming = false;
                reason += $"UpdatedDate is not set. ";
            }
            if (!reminderEvent.TryGetReminderType(out var _))
            {
                isConforming = false;
                reason += $"ReminderType missing or unknown. ";
            }
            if (reminderEvent.GetReminderSourceEventIds().Count == 0)
            {
                isConforming = false;
                reason += $"Missing source event IDs. ";
            }
            if (!isConforming)
            {
                _logger.LogWarning($"Reminder '{reminderEvent.Summary}' (ID: {reminderEvent.Id}) is not conforming: {reason}");
            }
            return isConforming;
        }

        private ReminderInfo MapCalendarEventToReminderInfo(Event reminderEvent, List<CalendarEvent> gigsOnDate)
        {
            var isOutdated = false;
            var outdatedReason = string.Empty;
            var relatedGigIds = new List<string>();
            var reminderType = ReminderType.Unknown;

            var extendedProperties = reminderEvent.ExtendedProperties?.Shared;
            var reminderUpdateTime = reminderEvent.UpdatedDateTimeOffset;

            var isConforming = IsConformingReminderEvent(reminderEvent, out outdatedReason);
            isOutdated = !isConforming;

            if (isConforming)
            {
                reminderType = reminderEvent.GetReminderType();
                relatedGigIds = reminderEvent.GetReminderSourceEventIds();

                var matchedGigsOnDate = gigsOnDate.Where(gig => relatedGigIds.Contains(gig.Id)).ToList();

                // Check if this is an orphaned reminder (all related gigs are missing)
                if (matchedGigsOnDate.Count == 0 && relatedGigIds.Count > 0)
                {
                    isOutdated = true;
                    outdatedReason = "All associated gig events have been deleted or moved outside the date range.";
                }
                else
                {
                    var missingFromReminderEventIds = gigsOnDate.Except(matchedGigsOnDate).ToList();
                    if (missingFromReminderEventIds.Count > 0)
                    {
                        isOutdated = true;
                        outdatedReason += $"Source gig event(s) no longer exist: {string.Join(", ", missingFromReminderEventIds.Select(ev => ev.Id))}. ";
                    }

                    var missingFromGigsOnDate = relatedGigIds.Except(gigsOnDate.Select(g => g.Id)).ToList();
                    if (missingFromGigsOnDate.Count > 0)
                    {
                        isOutdated = true;
                        outdatedReason += $"Reminder event(s) no longer exist: {string.Join(", ", missingFromGigsOnDate)}. ";
                    }

                    var updatedGigs = matchedGigsOnDate.Where(matchedGigsOnDate => matchedGigsOnDate.UpdatedDateTimeOffset > reminderUpdateTime!.Value).ToList();
                    if (updatedGigs.Count > 0)
                    {
                        isOutdated = true;
                        outdatedReason = $"Source gig events (ID: {string.Join(",", updatedGigs.Select(g => g.Id))}) were updated after this reminder was created/updated.";
                    }
                }
            }

            DateTimeOffset startTime, endTime;

            if (reminderEvent.Start.DateTimeDateTimeOffset != null)
            {
                // For timed events
                var rawStartTime = reminderEvent.Start.DateTimeDateTimeOffset ?? DateTimeOffset.MinValue;
                var rawEndTime = reminderEvent.End.DateTimeDateTimeOffset ?? DateTimeOffset.MinValue;

                // If the event has a timezone specified, use it; otherwise convert to Eastern
                if (string.IsNullOrEmpty(reminderEvent.Start.TimeZone))
                {
                    // No timezone specified, assume UTC and convert to Eastern
                    startTime = TimeZoneHelper.UtcToEasternTime(rawStartTime);
                    endTime = TimeZoneHelper.UtcToEasternTime(rawEndTime);
                }
                else
                {
                    // Timezone is already specified in the event, use as is
                    startTime = rawStartTime;
                    endTime = rawEndTime;
                }
            }
            else
            {
                // For all-day events
                startTime = TimeZoneHelper.ParseAllDayDateToEasternTimeZone(reminderEvent.Start.Date);
                endTime = TimeZoneHelper.ParseAllDayDateToEasternTimeZone(reminderEvent.End.Date);
            }

            // Check if the reminder is for a date in the past
            var isOld = startTime.Date < _clock.Today.Date;

            return new ReminderInfo
            {
                Date = startTime.Date,
                StartTime = startTime,
                EndTime = endTime,
                Title = reminderEvent.Summary,
                Description = reminderEvent.Description ?? string.Empty,
                Location = reminderEvent.Location,
                AlreadyExists = true,
                RelatedGigIds = relatedGigIds,
                IsOutdated = isOutdated,
                IsOld = isOld,
                OutdatedReason = outdatedReason,
                ReminderId = reminderEvent.Id,
                Type = reminderType
            };
        }
    }
}
