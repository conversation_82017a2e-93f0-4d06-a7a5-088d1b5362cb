// BandCalendarWebSite/Controllers/GigsController.cs
using BandCalendarApi.Models;
using BandCalendarLib;
using BandCalendarLib.Services;
using BandCalendarLib.Services.Util;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace BandCalendarApi.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class GigsController : ControllerBase
    {
        private readonly IGigCalendarService _gigCalendarService;
        private readonly IClock _clock;
        private readonly ILogger<GigsController> _logger;

        public GigsController(IGigCalendarService gigCalendarService, IClock clock, ILogger<GigsController> logger)
        {
            _gigCalendarService = gigCalendarService;
            _clock = clock;
            _logger = logger;
        }

        [HttpGet]
        public async Task<ActionResult<IEnumerable<CalendarEventDto>>> GetGigs(
            [FromQuery] DateTimeOffset? startDate = null,
            [FromQuery] DateTimeOffset? endDate = null,
            [FromQuery] string? location = null)
        {
            try
            {
                // Ensure dates are in Eastern Time
                var start = startDate.HasValue ? startDate.Value.EnsureEasternTime() : _clock.Today;
                var end = endDate.HasValue ? endDate.Value.EnsureEasternTime() : start.AddMonths(1);

                if (end < start)
                {
                    return BadRequest("End date must be after start date");
                }

                // Get all events (gigs and availability)
                var allEvents = await _gigCalendarService.GetAllEvents(start, end);
                var gigEvents = allEvents.Where(e => !e.IsAvailabilityEvent).ToList();
                var availabilityEvents = allEvents.Where(e => e.IsAvailabilityEvent).ToList();

                // Filter gigs by location if provided
                if (!string.IsNullOrWhiteSpace(location))
                {
                    gigEvents = gigEvents.Where(e =>
                        !string.IsNullOrEmpty(e.Location) &&
                        e.Location.Contains(location, StringComparison.OrdinalIgnoreCase)
                    ).ToList();
                }

                // Create DTOs for all events (gigs and availability)
                var allEventDtos = new List<CalendarEventDto>();

                // Add gig events with unavailability information
                var gigDtos = gigEvents.Select(e => {
                    // Find unavailability events on the same day
                    var unavailableMembers = FindUnavailableMembersOnSameDay(e, availabilityEvents);
                    
                    return new CalendarEventDto
                    {
                        Id = e.Id,
                        Summary = e.Summary,
                        Description = e.Description,
                        Location = e.Location,
                        MapLink = CreateGoogleMapsUrl(e.Location),
                        HtmlLink = e.HtmlLink,
                        StartTime = e.StartTime,
                        EndTime = e.EndTime,
                        IsAllDay = e.IsAllDay,
                        IsAvailabilityEvent = e.IsAvailabilityEvent,
                        UnavailableMembers = unavailableMembers
                    };
                });

                // Add availability events as separate calendar entries
                var availabilityDtos = availabilityEvents.Select(e => new CalendarEventDto
                {
                    Id = e.Id,
                    Summary = e.Summary,
                    Description = e.Description,
                    Location = e.Location,
                    MapLink = string.Empty, // No map link needed for availability events
                    HtmlLink = e.HtmlLink,
                    StartTime = e.StartTime,
                    EndTime = e.EndTime,
                    IsAllDay = e.IsAllDay,
                    IsAvailabilityEvent = e.IsAvailabilityEvent,
                    UnavailableMembers = new List<UnavailabilityInfo>() // Availability events don't have unavailable members
                });

                // Combine both types of events
                allEventDtos.AddRange(gigDtos);
                allEventDtos.AddRange(availabilityDtos);

                // Sort by start time
                allEventDtos = allEventDtos.OrderBy(e => e.StartTime).ToList();

                return Ok(allEventDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving gigs");
                return StatusCode(500, "An error occurred while retrieving gigs");
            }
        }

        // Get all unique locations for dropdown
        [HttpGet("locations")]
        public async Task<ActionResult<IEnumerable<string>>> GetLocations()
        {
            try
            {
                var startDate = _clock.Today.AddDays(-2);
                var endDate = _clock.Today.AddYears(1);

                var events = await _gigCalendarService.GetGigEvents(startDate, endDate);

                // Extract unique non-empty locations
                var uniqueLocations = events
                    .Where(e => !string.IsNullOrWhiteSpace(e.Location))
                    .Select(e => e.Location)
                    .Distinct()
                    .OrderBy(l => l)
                    .ToList();
                return Ok(uniqueLocations);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving locations");
                return StatusCode(500, "An error occurred while retrieving locations");
            }
        }

        private string CreateGoogleMapsUrl(string location)
        {
            if (string.IsNullOrEmpty(location))
                return string.Empty;

            // URL encode the location for use in the query parameter
            var encodedLocation = Uri.EscapeDataString(location);
            return $"https://www.google.com/maps/search/?api=1&query={encodedLocation}";
        }

        private List<UnavailabilityInfo> FindUnavailableMembersOnSameDay(
            BandCalendarLib.Models.CalendarEvent gigEvent, 
            List<BandCalendarLib.Models.CalendarEvent> availabilityEvents)
        {
            var unavailableMembers = new List<UnavailabilityInfo>();
            
            // Extract the date part only (ignoring time) for comparison
            var gigDate = gigEvent.StartTime;
            
            // Find availability events that overlap with this gig date
            var overlappingEvents = availabilityEvents.Where(ae =>
                ae.StartTime <= gigDate && ae.EndTime > gigDate
            ).ToList();
            
            foreach (var availEvent in overlappingEvents)
            {
                // Parse the summary to extract member name
                var summary = availEvent.Summary;
                if (string.IsNullOrEmpty(summary))
                    continue;
                
                var splitChars = new[] { ' ', '-', ':', '/' };
                var parts = summary.Split(splitChars, 2, StringSplitOptions.RemoveEmptyEntries | StringSplitOptions.TrimEntries);
                
                if (parts.Length >= 1)
                {
                    var memberName = parts[0];
                    
                    // Capitalize the first letter of the member name
                    if (!string.IsNullOrEmpty(memberName) && memberName.Length > 0)
                    {
                        memberName = char.ToUpper(memberName[0]) + memberName.Substring(1).ToLower();
                    }
                    
                    unavailableMembers.Add(new UnavailabilityInfo
                    {
                        MemberName = memberName,
                        AvailabilityEventId = availEvent.Id
                    });
                }
            }
            
            return unavailableMembers;
        }
    }
}