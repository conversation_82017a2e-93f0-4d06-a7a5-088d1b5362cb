// BandCalendarWebSite/Controllers/RemindersController.cs
using BandCalendarApi.Models;
using BandCalendarLib;
using BandCalendarLib.Models;
using BandCalendarLib.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace BandCalendarApi.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class RemindersController : ControllerBase
    {
        private readonly IReminderService _reminderService;
        private readonly IClock _clock;
        private readonly ILogger<RemindersController> _logger;

        public RemindersController(IReminderService reminderService, IClock clock, ILogger<RemindersController> logger)
        {
            _reminderService = reminderService;
            _clock = clock;
            _logger = logger;
        }

        [HttpGet]
        public async Task<ActionResult<IEnumerable<ReminderInfoDto>>> GetPendingReminders(
            [FromQuery] DateTimeOffset? startDate = null,
            [FromQuery] DateTimeOffset? endDate = null,
            [FromQuery] string? type = null)
        {
            try
            {
                // Ensure dates are in Eastern Time
                var start = startDate.HasValue ? startDate.Value.EnsureEasternTime() : _clock.Today.AddMonths(-1);
                var end = endDate.HasValue ? endDate.Value.EnsureEasternTime() : _clock.Today.AddMonths(1);

                if (end < start)
                {
                    return BadRequest("End date must be after start date");
                }

                // Convert string type parameter to ReminderType enum if provided
                ReminderType? reminderType = null;
                if (!string.IsNullOrEmpty(type))
                {
                    if (Enum.TryParse<ReminderType>(type, true, out var parsedType))
                    {
                        reminderType = parsedType;
                    }
                    else
                    {
                        return BadRequest($"Invalid reminder type: {type}");
                    }
                }

                var reminders = await _reminderService.GetRemindersAsync(start, end, reminderType);

                return Ok(reminders.Select(r => new ReminderInfoDto
                {
                    Date = r.Date,
                    StartTime = r.StartTime,
                    EndTime = r.EndTime,
                    Title = r.Title,
                    Description = r.Description,
                    Location = r.Location,
                    AlreadyExists = r.AlreadyExists,
                    Created = r.Created,
                    RelatedGigId = r.RelatedGigIds.FirstOrDefault() ?? string.Empty, //TODO: Handle multiple related gig IDs
                    IsOutdated = r.IsOutdated,
                    IsOld = r.IsOld,
                    OutdatedReason = r.OutdatedReason,
                    ReminderId = r.ReminderId,
                    Type = r.Type.ToString()
                }));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving pending reminders");
                return StatusCode(500, "An error occurred while retrieving reminders");
            }
        }

        [HttpPost]
        public async Task<ActionResult<IEnumerable<ReminderInfoDto>>> CreateReminders(
            [FromBody] CreateRemindersRequest request)
        {
            try
            {
                if (request.EndDate < request.StartDate)
                {
                    return BadRequest("End date must be after start date");
                }

                // Convert string type parameter to ReminderType enum if provided
                ReminderType? reminderType = null;
                if (!string.IsNullOrEmpty(request.Type))
                {
                    if (Enum.TryParse<ReminderType>(request.Type, true, out var parsedType))
                    {
                        reminderType = parsedType;
                    }
                    else
                    {
                        return BadRequest($"Invalid reminder type: {request.Type}");
                    }
                }

                var createdReminders = await _reminderService.CreateReminderEventsAsync(
                    request.StartDate, request.EndDate, reminderType);

                return Ok(createdReminders.Select(r => new ReminderInfoDto
                {
                    Date = r.Date,
                    StartTime = r.StartTime,
                    EndTime = r.EndTime,
                    Title = r.Title,
                    Description = r.Description,
                    Location = r.Location,
                    AlreadyExists = r.AlreadyExists,
                    Created = r.Created,
                    RelatedGigId = r.RelatedGigIds.FirstOrDefault() ?? string.Empty, //TODO: Handle multiple related gig IDs
                    IsOutdated = r.IsOutdated,
                    IsOld = r.IsOld,
                    OutdatedReason = r.OutdatedReason,
                    ReminderId = r.ReminderId,
                    Type = r.Type.ToString()
                }));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating reminders");
                return StatusCode(500, "An error occurred while creating reminders");
            }
        }

        [HttpDelete("{reminderId}")]
        public async Task<ActionResult> DeleteReminder(string reminderId)
        {
            try
            {
                if (string.IsNullOrEmpty(reminderId))
                {
                    return BadRequest("Reminder ID is required");
                }

                var success = await _reminderService.DeleteReminderEventAsync(reminderId);

                if (success)
                {
                    return Ok(new { success = true, message = "Reminder deleted successfully" });
                }
                else
                {
                    return NotFound(new { success = false, message = "Reminder not found or could not be deleted" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting reminder");
                return StatusCode(500, "An error occurred while deleting the reminder");
            }
        }

        [HttpPost("delete")]
        public async Task<ActionResult> DeleteReminders([FromBody] DeleteRemindersRequest request)
        {
            try
            {
                if (request?.ReminderIds == null || !request.ReminderIds.Any())
                {
                    return BadRequest("At least one reminder ID is required");
                }

                var results = await _reminderService.DeleteReminderEventsAsync(request.ReminderIds);
                var allSucceeded = results.Values.All(v => v);
                var successCount = results.Values.Count(v => v);

                if (allSucceeded)
                {
                    return Ok(new { success = true, message = $"All {successCount} reminders deleted successfully" });
                }
                else
                {
                    return Ok(new {
                        success = successCount > 0,
                        message = $"{successCount} out of {request.ReminderIds.Count} reminders deleted successfully",
                        results = results
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting multiple reminders");
                return StatusCode(500, "An error occurred while deleting the reminders");
            }
        }
    }
}
