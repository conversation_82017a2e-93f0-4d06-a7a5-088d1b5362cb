using BandCalendarWebSite.Server.Util.Auth;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.Cookies;

namespace BandCalendarWebSite.Server.Middleware
{
    /// <summary>
    /// Middleware that checks if the Google API tokens have expired and signs out the user if they have.
    /// </summary>
    public class TokenExpirationMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILogger<TokenExpirationMiddleware> _logger;

        public TokenExpirationMiddleware(RequestDelegate next, ILogger<TokenExpirationMiddleware> logger)
        {
            _next = next;
            _logger = logger;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            // Only check token expiration for authenticated users
            if (context.User.Identity?.IsAuthenticated == true)
            {
                // Skip token check for authentication-related endpoints to avoid redirect loops
                if (!context.Request.Path.StartsWithSegments("/signin-google") &&
                    !context.Request.Path.StartsWithSegments("/api/auth"))
                {
                    var tokenExpired = await IsTokenExpiredAsync(context);

                    if (tokenExpired)
                    {
                        _logger.LogInformation("Google API token has expired. Signing out user {UserId}.",
                            context.User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value);

                        // Sign out the user
                        await context.SignOutAsync(CookieAuthenticationDefaults.AuthenticationScheme);

                        // If this is an API request, return 401 Unauthorized
                        if (context.Request.Path.StartsWithSegments("/api") &&
                            !context.Request.Path.StartsWithSegments("/api/auth") &&
                            context.Request.Headers.Accept.ToString().Contains("application/json"))
                        {
                            context.Response.StatusCode = StatusCodes.Status401Unauthorized;
                            return;
                        }

                        // Otherwise redirect to login page
                        context.Response.Redirect("/login");
                        return;
                    }
                }
            }

            // Continue processing the request
            await _next(context);
        }

        private async Task<bool> IsTokenExpiredAsync(HttpContext context)
        {
            try
            {
                // Use the extension method to get the token response
                var tokenResponse = await context.GetGoogleTokenResponseAsync();

                // Check if the token is stale (expired)
                var isExpired = tokenResponse.IsStale;

                if (isExpired)
                {
                    _logger.LogInformation("Token is stale or expired. Issued: {IssuedUtc}, Expires in: {ExpiresInSeconds} seconds",
                        tokenResponse.IssuedUtc, tokenResponse.ExpiresInSeconds);
                }

                return isExpired;
            }
            catch (UnauthorizedAccessException ex)
            {
                // This exception is thrown when tokens are missing or invalid
                _logger.LogWarning(ex, "Token validation failed: {Message}", ex.Message);
                return true; // Consider token expired if validation fails
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking token expiration");
                return false; // Don't sign out on error to avoid potential denial of service
            }
        }
    }

    // Extension method to make it easier to add the middleware to the request pipeline
    public static class TokenExpirationMiddlewareExtensions
    {
        public static IApplicationBuilder UseTokenExpirationCheck(this IApplicationBuilder builder)
        {
            return builder.UseMiddleware<TokenExpirationMiddleware>();
        }
    }
}
