// BandCalendarApi/Models/CalendarEventDto.cs
using System;
using System.Collections.Generic;

namespace BandCalendarApi.Models
{
    public class CalendarEventDto
    {
        public string Id { get; set; } = string.Empty;
        public string Summary { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Location { get; set; } = string.Empty;
        public string MapLink { get; set; } = string.Empty;
        public string HtmlLink { get; set; } = string.Empty;
        public DateTimeOffset StartTime { get; set; }
        public DateTimeOffset EndTime { get; set; }
        public bool IsAllDay { get; set; }
        public bool IsAvailabilityEvent { get; set; }
        public List<UnavailabilityInfo> UnavailableMembers { get; set; } = new List<UnavailabilityInfo>();
    }

    public class UnavailabilityInfo
    {
        public string MemberName { get; set; } = string.Empty;
        public string AvailabilityEventId { get; set; } = string.Empty;
    }
}
