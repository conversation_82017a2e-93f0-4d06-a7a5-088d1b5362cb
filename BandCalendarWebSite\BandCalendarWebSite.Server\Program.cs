using Azure.Identity;
using BandCalendarLib.DependencyInjection;
using BandCalendarLib.Models;
using BandCalendarWebSite.Server.Middleware;
using BandCalendarWebSite.Server.Services;
using Google.Apis.Calendar.v3;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authentication.Google;
using Microsoft.AspNetCore.Authentication.OAuth;
using Microsoft.OpenApi.Models;
using Serilog;

// Create a bootstrap logger for startup
Log.Logger = new LoggerConfiguration()
    .Enrich.FromLogContext()
    .WriteTo.Console()
    .CreateBootstrapLogger();

try
{
    var builder = WebApplication.CreateBuilder(args);

    if (builder.Environment.IsProduction())
    {
        builder.Services.AddApplicationInsightsTelemetry(options =>
        {
            options.ConnectionString = builder.Configuration["ApplicationInsights:ConnectionString"];
        });

        builder.Configuration.AddAzureKeyVault(
            new Uri(builder.Configuration["AzureKeyVault:KeyVaultUri"] ?? throw new InvalidOperationException("Missing AzureKeyVault:KeyVaultUri in configuration")),
            new DefaultAzureCredential());
    }
    else
    {
        builder.Services.AddEndpointsApiExplorer();
        builder.Services.AddSwaggerGen(c =>
        {
            c.SwaggerDoc("v1", new OpenApiInfo
            {
                Title = "Band Calendar API",
                Version = "v1",
                Description = "API for band gig calendar and reminders"
            });
        });

        builder.Configuration.AddUserSecrets<Program>();
    }

    builder.Host.UseSerilog((context, services, configuration) => configuration
        .ReadFrom.Configuration(context.Configuration)
        .ReadFrom.Services(services)
        .Enrich.FromLogContext());

    builder.Services.AddControllers()
        .AddJsonOptions(options =>
        {
            // Configure JSON serialization to use invariant culture for dates
            options.JsonSerializerOptions.Converters.Add(new System.Text.Json.Serialization.JsonStringEnumConverter());
        });

    builder.Services.Configure<GoogleApiConfig>(builder.Configuration.GetSection("Google"));

    builder.Services.AddSingleton(provider =>
        provider.GetRequiredService<Microsoft.Extensions.Options.IOptions<GoogleApiConfig>>().Value);

    builder.Services.AddHttpContextAccessor();

    builder.Services.AddScoped<GoogleCalendarServiceFactory>();
    builder.Services.AddBandCalendarLib(options =>
    {
        options.CalendarServiceFactory = sp =>
        {
            var factory = sp.GetRequiredService<GoogleCalendarServiceFactory>();
            return factory.GetCalendarServiceAsync().GetAwaiter().GetResult();
        };
    });

    builder.Services.AddAuthentication(options =>
    {
        options.DefaultScheme = CookieAuthenticationDefaults.AuthenticationScheme;
        options.DefaultChallengeScheme = GoogleDefaults.AuthenticationScheme;
    })
    .AddCookie(options =>
    {
        options.Cookie.HttpOnly = true;
        options.Cookie.SameSite = SameSiteMode.Lax; // Changed from Strict to allow redirects from Google
        options.Cookie.SecurePolicy = CookieSecurePolicy.Always;
        options.ExpireTimeSpan = TimeSpan.FromDays(7);
        options.SlidingExpiration = false;

        options.Events = new CookieAuthenticationEvents
        {
            OnRedirectToLogin = CreateRedirectHandler<CookieAuthenticationOptions>(StatusCodes.Status401Unauthorized),
            OnRedirectToAccessDenied = CreateRedirectHandler<CookieAuthenticationOptions>(StatusCodes.Status403Forbidden)
        };
    })
    .AddGoogle(options =>
    {
        options.ClientId = builder.Configuration["Google:ClientId"] ?? throw new InvalidOperationException("Google:sClientId must be specified");
        options.ClientSecret = builder.Configuration["Google:ClientSecret"] ?? throw new InvalidOperationException("Google:ClientSecret must be specified");
        options.SaveTokens = true;
        options.Scope.Add(CalendarService.Scope.CalendarReadonly);
        options.Scope.Add(CalendarService.Scope.Calendar);

        options.Events = new OAuthEvents
        {
            OnRedirectToAuthorizationEndpoint = CreateRedirectHandler<OAuthOptions>(StatusCodes.Status401Unauthorized)
        };
    });

    var app = builder.Build();

    // Log a test message to verify logging is working
    Log.Information("Application starting up");

    app.UseHttpsRedirection();
    app.UseDefaultFiles();
    app.UseStaticFiles();

    if (app.Environment.IsDevelopment())
    {
        app.UseSwagger();
        app.UseSwaggerUI();
    }

    app.UseSerilogRequestLogging();
    app.UseRouting();

    app.UseAuthentication();
    app.UseTokenExpirationCheck();
    app.UseAuthorization();

    app.MapControllers();

    app.MapFallbackToFile("/index.html");

    app.Run();
}
catch (Exception ex)
{
    Log.Fatal(ex, "Application terminated unexpectedly");
}
finally
{
    // Ensure to flush and stop internal timers/threads before application exit
    Log.CloseAndFlush();
}

Func<RedirectContext<TOptions>, Task> CreateRedirectHandler<TOptions>(int statusCode) where TOptions : AuthenticationSchemeOptions => ctx =>
{
    if (ctx.Request.Path.StartsWithSegments("/api") && !ctx.Request.Path.StartsWithSegments("/api/auth"))
    {
        ctx.Response.StatusCode = statusCode;
        return Task.CompletedTask;
    }

    ctx.Response.Redirect(ctx.RedirectUri);
    return Task.CompletedTask;
};
