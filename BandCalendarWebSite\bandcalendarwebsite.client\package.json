{"name": "bandcalendarwebsite.client", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "dev:mock": "cross-env VITE_ENABLE_MOCKS=true vite", "build": "tsc -b && vite build", "build:mock": "cross-env VITE_ENABLE_MOCKS=true tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "msw:init": "msw init public/ --save"}, "dependencies": {"@react-google-maps/api": "^2.20.6", "@types/react-datepicker": "^6.2.0", "axios": "^1.8.4", "bootstrap-icons": "^1.11.3", "date-fns": "^4.1.0", "moment": "^2.30.1", "react": "^19.0.0", "react-big-calendar": "^1.19.2", "react-datepicker": "^8.3.0", "react-dom": "^19.0.0", "react-router-dom": "^7.5.2"}, "devDependencies": {"@eslint/js": "^9.21.0", "@types/node": "^22", "@types/react": "^19.0.10", "@types/react-big-calendar": "^1.16.2", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "cross-env": "^7.0.3", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "msw": "^2.7.5", "typescript": "~5.7.2", "typescript-eslint": "^8.24.1", "vite": "^6.3.4", "vitest": "^3.1.2"}, "msw": {"workerDirectory": ["public"]}}