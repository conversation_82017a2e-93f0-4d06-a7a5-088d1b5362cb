#root {
  margin: 0 auto;
  text-align: center;
  width: 100%;
  max-width: none;
  padding: 0;
}

/* Ensure root takes full width when on login page */
body.login-page #root {
  width: 100vw;
  max-width: 100vw;
  height: 100vh;
  overflow: hidden;
  background-color: var(--primary-bg);
}

/* Login page container - full width with no padding */
.login-container {
  width: 100vw;
  max-width: 100vw;
  padding: 0;
  margin: 0;
  height: 100vh;
  background-color: var(--primary-bg);
}

/* Map container - full width with no padding */
.map-container {
  width: 100%;
  max-width: 100%;
  padding: 0;
  margin: 0;
  position: relative;
}

/* Override body styles for login page */
body.login-page {
  margin: 0;
  padding: 0;
  overflow: hidden;
  background-color: var(--primary-bg);
  height: 100vh;
  width: 100vw;
}

/* Mobile-specific styles */
.mobile-layout-container .container {
  padding-top: 0;
  margin-top: 0;
  max-width: 100%;
  transition: margin-top 0.3s ease;
}

/* Desktop-specific styles */
@media (min-width: 769px) {
  /* Specific styles for desktop layout */
  .desktop-layout-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    position: relative;
    padding-top: 0 !important; /* No top padding needed with relative navbar */
  }

  .container {
    margin-top: 20px !important; /* Add space below navbar */
  }

  .app-layout {
    margin-top: 20px; /* Add space between navbar and content */
  }
}

/* Adjust container spacing when offline indicator is shown */
body.has-offline-indicator .mobile-layout-container .container {
  margin-top: 0; /* The padding is handled by the OfflineIndicator component */
}
