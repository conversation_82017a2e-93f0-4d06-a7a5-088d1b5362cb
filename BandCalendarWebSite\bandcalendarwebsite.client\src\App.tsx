import {BrowserRouter as Router, Navigate, Route, Routes, useLocation} from 'react-router-dom';
import EventsPage from './pages/Events/EventsPage.tsx';
import CalendarPage from './pages/Calendar/CalendarPage.tsx';
import AnalyticsPage from './pages/Analytics/AnalyticsPage.tsx';
import RemindersPage from './pages/Reminders/RemindersPage.tsx';
import MapPage from './pages/Map/MapPage.tsx';
import UpdateNotification from './components/UpdateNotification';
import OfflineIndicator from './components/OfflineIndicator';
import LoginPage from './pages/Login/LoginPage.tsx';
import Navbar from './components/Navbar';
import FilterMenu from './components/FilterMenu';
import SidebarFilterMenu from './components/SidebarFilterMenu';
import {useEffect, useState} from 'react';
import {useNetworkStatus} from './contexts/NetworkStatusContext';
// Import the axios configuration
import './api/axiosConfig';

// Import loading styles
import './components/LoadingStyles.css';
import {ProtectedRoute} from "./ProtectedRoute.tsx";

// Main content component with access to router context
const AppContent = () => {
    const location = useLocation();
    const { isOnline, checkOfflineCache } = useNetworkStatus();
    const [ isMobile, setIsMobile ] = useState(window.innerWidth <= 768);
    const [ isIOS, setIsIOS ] = useState(false);
    const [ isPWA, setIsPWA ] = useState(false);
    const [ filterMenuOpen, setFilterMenuOpen ] = useState(false);

    interface RouteProperties {
        hasFilters : boolean;
        isLogin : boolean;
    }

    const routePropertiesMap : {[key: string]: RouteProperties} = {
        '/login': { hasFilters: false, isLogin: true },
        '/events': { hasFilters: true, isLogin: false },
        '/calendar': { hasFilters: true, isLogin: false },
        '/map': { hasFilters: false, isLogin: false },
        '/reminders': { hasFilters: true, isLogin: false },
        '/analytics': { hasFilters: false, isLogin: false },
    };

    const getRouteProperties = () =>
        routePropertiesMap[location.pathname] || { hasFilters: false, isLogin: false };

    // Check for offline cache flag when component mounts
    useEffect(() => {
        checkOfflineCache();
    }, [checkOfflineCache]);

    // Function to handle app updates
    const handleAppUpdate = () => {
        // Reload the page to get the latest version
        window.location.reload();
    };

    // Toggle filter menu
    const toggleFilterMenu = () => {
        const newState = !filterMenuOpen;
        setFilterMenuOpen(newState);
        // The FilterMenu component will now handle dispatching the filter-menu-opened event
    };

    // Check for mobile screen size, iOS, and PWA mode on mount and resize
    useEffect(() => {
        const handleResize = () => {
            setIsMobile(window.innerWidth <= 768);
        };

        // Detect iOS
        const isIOSDevice = /iPad|iPhone|iPod/.test(navigator.userAgent) ||
            (navigator.maxTouchPoints > 1 && /MacIntel/.test(navigator.userAgent));
        setIsIOS(isIOSDevice);

        // Detect if running as PWA (standalone mode)
        const isPWAMode = window.matchMedia('(display-mode: standalone)').matches ||
            (window.navigator as any).standalone === true;
        setIsPWA(isPWAMode);

        window.addEventListener('resize', handleResize);
        return () => window.removeEventListener('resize', handleResize);
    }, []);

    // Add offline class to body when offline
    useEffect(() => {
        if (!isOnline) {
            document.body.classList.add('offline-mode');
        } else {
            document.body.classList.remove('offline-mode');
        }

        return () => {
            document.body.classList.remove('offline-mode');
        };
    }, [isOnline]);

    // Adjust content area padding when offline indicator is visible
    useEffect(() => {
        const adjustLayout = () => {
            const hasOfflineIndicator = document.body.classList.contains('has-offline-indicator');
            const contentAreas = document.querySelectorAll('.content-area');

            contentAreas.forEach(area => {
                if (hasOfflineIndicator) {
                    // Ensure content doesn't get hidden behind the offline indicator
                    if (isMobile) {
                        (area as HTMLElement).style.marginTop = '36px';
                    }
                } else {
                    (area as HTMLElement).style.marginTop = '';
                }
            });
        };

        // Run initially and when the offline status changes
        adjustLayout();

        // Also listen for the class change on body
        const observer = new MutationObserver(adjustLayout);
        observer.observe(document.body, {
            attributes: true,
            attributeFilter: ['class']
        });

        return () => observer.disconnect();
    }, [isMobile]);

    // Initialize layout after navigation/login
    useEffect(() => {
        if (location.pathname !== '/login') {
            // Force layout recalculation after login
            const initializeLayout = () => {
                // Force browser to recalculate layout
                document.body.style.display = 'none';
                // Use requestAnimationFrame to ensure the style change is applied
                requestAnimationFrame(() => {
                    document.body.style.display = '';
                });
            };

            // Small delay to ensure all components are mounted
            const timer = setTimeout(initializeLayout, 100);
            return () => clearTimeout(timer);
        }
    }, [location.pathname]);

    // Determine container class based on path
    const getContainerClass = () => {
        if (location.pathname === '/login') return 'login-container';
        if (location.pathname === '/map') {
            // Special handling for map container in iOS PWA mode
            if (isIOS && isPWA) {
                return 'map-container content-area ios-pwa-container';
            }
            return 'map-container content-area';
        }
        return isMobile ? 'container mt-2 content-area' : 'container mt-4';
    };

    // Add special classes for iOS PWA mode and offline mode
    const getLayoutClass = () => {
        if (location.pathname === '/login') return '';

        const classes = [];
        if (isMobile) {
            classes.push('mobile-layout-container');
        } else {
            classes.push('desktop-layout-container'); // Add specific class for desktop
        }
        if (isIOS && isPWA) classes.push('ios-pwa-mode');
        if (!isOnline) classes.push('offline-mode');

        return classes.join(' ');
    };

    const routeProperties = getRouteProperties();
    return (
        <div className={getLayoutClass()}>
            { !routeProperties.isLogin &&
                <>
                    <UpdateNotification onReload={handleAppUpdate} />
                    <OfflineIndicator />
                    <Navbar
                        onToggleFilters={toggleFilterMenu}
                        showFilterButton={isMobile && routeProperties.hasFilters}
                    />
                </>
            }

            { isMobile && routeProperties.hasFilters &&
                <FilterMenu isOpen={filterMenuOpen} onClose={toggleFilterMenu}>
                    <div id="filter-container" className="filter-container-wrapper">
                        {/* This div will be populated by individual pages  */}
                    </div>
                </FilterMenu>
            }

            <div className="app-layout">
                <div className={getContainerClass()}>
                    <Routes>
                        <Route path="/login" element={<LoginPage />} />
                        <Route path="/" element={<ProtectedRoute><Navigate to="/events" replace /></ProtectedRoute>} />
                        <Route path="/events" element={<ProtectedRoute><EventsPage isMobile={isMobile} /></ProtectedRoute>} />
                        <Route path="/calendar" element={<ProtectedRoute><CalendarPage isMobile={isMobile} /></ProtectedRoute>} />
                        <Route path="/map" element={<ProtectedRoute><MapPage isIOS={isIOS} isPWA={isPWA} /></ProtectedRoute>} />
                        <Route path="/analytics" element={<ProtectedRoute><AnalyticsPage /></ProtectedRoute>} />
                        <Route path="/reminders" element={<ProtectedRoute><RemindersPage isMobile={isMobile} /></ProtectedRoute>} />
                    </Routes>
                </div>

                { !isMobile && routeProperties.hasFilters && (
                    <SidebarFilterMenu>
                        <div id="filter-container" className="filter-container-wrapper">
                            {/* This div will be populated by the Events component */}
                        </div>
                    </SidebarFilterMenu>
                )}
            </div>
        </div>
    );
};

// Main App component that provides the Router
const App = () => {
    return (
        <Router>
            <AppContent />
        </Router>
    );
};

export default App;
