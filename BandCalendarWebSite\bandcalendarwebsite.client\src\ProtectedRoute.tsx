// Protected route component
import {useEffect, useState} from "react";
import {Navigate, useLocation} from "react-router-dom";
import axios from "axios";

export const ProtectedRoute = ({children}: { children: React.ReactNode }) => {
    const [isAuthenticated, setIsAuthenticated] = useState<boolean | null>(null);
    const [isLoading, setIsLoading] = useState(true);
    const location = useLocation();

    useEffect(() => {
        const checkAuth = async () => {
            try {
                const response = await axios.get('/api/auth/status');
                setIsAuthenticated(response.data.isAuthenticated);
            } catch {
                setIsAuthenticated(false);
            } finally {
                setIsLoading(false);
            }
        };

        checkAuth();
    }, []);

    if (isLoading) {
        return (
            <div className="loading-container content-area">
                <div className="loading-indicator">Loading...</div>
            </div>
        );
    }

    if (!isAuthenticated) {
        // Redirect to login page with the current location
        return <Navigate to="/login" state={{from: location}} replace/>;
    }

    return <>{children}</>;
};