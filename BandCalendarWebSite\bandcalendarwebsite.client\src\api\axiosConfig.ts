import axios from 'axios';

// Configure axios to always send credentials
axios.defaults.withCredentials = true;

export interface ApiError {
    isOffline: boolean;
    message: string;
    originalError: never;
}

// Add response interceptor to handle offline responses and errors
axios.interceptors.response.use(
    response => {
        // Check if the response came from the service worker cache
        const fromCache = response.headers['sw-from-cache'];
        const fetchedOn = response.headers['sw-fetched-on'];

        if (fromCache) {
            // Add a flag to the response data to indicate it's from cache
            if (typeof response.data === 'object' && response.data !== null) {
                response.data.fromCache = true;
                if (fetchedOn) {
                    response.data.cachedOn = fetchedOn;
                }
            }

            // Dispatch an event to notify the app about offline data
            window.dispatchEvent(new CustomEvent('offline-data-used', {
                detail: { url: response.config.url }
            }));
        }

        return response;
    },
    error => {
        // Check if this is a network error (offline)
        if (!error.response && error.message === 'Network Error') {
            // Dispatch an event to notify the app about offline status
            window.dispatchEvent(new Event('app-offline'));

            console.warn('Network error detected, app is offline');

            // For API requests, return a custom error object
            if (error.config?.url?.includes('/api/')) {
                return Promise.reject(<ApiError>{
                    isOffline: true,
                    message: 'You are offline and this data is not available.',
                    originalError: error
                });
            }
        }
        // Handle HTTP error responses
        else if (error.response) {
            if (error.response.status === 401) {
                // Store the current URL to redirect back after login
                const currentPath = window.location.pathname + window.location.search;
                console.warn(`Unauthorized access to ${currentPath}, redirecting to login`);

                // Redirect to login page for 401 Unauthorized responses
                window.location.href = `/login?returnUrl=${encodeURIComponent(currentPath)}`;
            } else if (error.response.status === 403) {
                // Handle forbidden errors
                console.error('Access forbidden');
            } else if (error.response.status === 503 && error.response.data?.offline) {
                // This is our custom offline response from the service worker
                window.dispatchEvent(new Event('app-offline'));

                return Promise.reject(<ApiError>{
                    isOffline: true,
                    message: error.response.data.error || 'You are offline and this data is not available.',
                    originalError: error
                });
            }
        }

        return Promise.reject(error);
    }
);