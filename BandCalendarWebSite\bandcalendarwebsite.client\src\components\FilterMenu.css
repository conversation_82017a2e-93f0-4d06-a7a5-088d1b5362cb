/* Filter Menu Styles */
.filter-menu-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1100;
  opacity: 0;
  transition: opacity 0.2s ease-in-out;
}

.filter-menu-backdrop.visible {
  opacity: 1;
}

.filter-menu {
  position: fixed;
  top: calc(48px + env(safe-area-inset-top, 0));
  right: 0;
  width: 85%;
  max-width: 350px;
  height: calc(100% - 48px - env(safe-area-inset-top, 0) - 56px - env(safe-area-inset-bottom, 0));
  background-color: var(--bg-main);
  z-index: 1101;
  box-shadow: -2px 0 10px var(--shadow-color);
  display: flex;
  flex-direction: column;
  transform: translateX(100%);
  transition: transform 0.3s ease-in-out, top 0.3s ease, height 0.3s ease;
  overflow-y: auto;
}

.filter-menu.visible {
  transform: translateX(0);
}

/* Adjust for offline indicator */
body.has-offline-indicator .filter-menu {
  top: calc(48px + env(safe-area-inset-top, 0) + 36px);
  height: calc(100% - 48px - env(safe-area-inset-top, 0) - 56px - env(safe-area-inset-bottom, 0) - 36px);
}

/* Header */
.filter-menu-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid var(--bg-secondary);
}

.filter-menu-header h5 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
}

.filter-menu-close {
  background: none;
  border: none;
  font-size: 1.2rem;
  cursor: pointer;
  color: var(--accent);
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
}

/* Content */
.filter-menu-content {
  padding: 16px;
  flex: 1;
  overflow-y: auto;
}

.filter-container-wrapper {
  width: 100%;
  height: 100%;
}

/* Filter style overrides */
.filter-menu .compact-filters {
  margin: 0;
  padding: 0;
  box-shadow: none;
  border: none;
}

