import React, { useState, useEffect, useRef } from 'react';
import './FilterMenu.css';

interface FilterMenuProps {
  isOpen: boolean;
  onClose: () => void;
  children: React.ReactNode;
}

const FilterMenu: React.FC<FilterMenuProps> = ({ isOpen, onClose, children }) => {
  const [isVisible, setIsVisible] = useState(false);
  const [shouldRender, setShouldRender] = useState(false);
  const backdropRef = useRef<HTMLDivElement>(null);
  const menuRef = useRef<HTMLDivElement>(null);

  // Handle opening and closing animations
  useEffect(() => {
    if (isOpen) {
      setShouldRender(true);
      // Small delay to ensure the component is rendered before adding the visible class
      setTimeout(() => {
        setIsVisible(true);
        // Dispatch the filter-menu-opened event when the menu becomes visible
        setTimeout(() => {
          window.dispatchEvent(new Event('filter-menu-opened'));
        }, 50);
      }, 10);
    } else {
      setIsVisible(false);
      // Wait for the animation to complete before removing from DOM
      const timeout = setTimeout(() => setShouldRender(false), 300);
      return () => clearTimeout(timeout);
    }
  }, [isOpen]);

  // Don't render anything if the menu shouldn't be in the DOM
  if (!shouldRender) return null;

  return (
    <>
      <div
        ref={backdropRef}
        className={`filter-menu-backdrop ${isVisible ? 'visible' : 'hidden'}`}
        onClick={onClose}
      ></div>
      <div
        ref={menuRef}
        className={`filter-menu ${isVisible ? 'visible' : 'hidden'}`}
      >
        <div className="filter-menu-header">
          <h5>Filters</h5>
          <button className="filter-menu-close" onClick={onClose}>
            <i className="bi bi-x-lg"></i>
          </button>
        </div>
        <div className="filter-menu-content">
          {children}
        </div>
      </div>
    </>
  );
};

export default FilterMenu;
