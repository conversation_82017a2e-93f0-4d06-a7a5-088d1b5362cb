/* Loading styles to prevent layout shifts */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
  width: 100%;
  position: relative;
  flex: 1;
}

.loading-indicator {
  padding: 10px 20px;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  z-index: 10;
  position: absolute;
}

/* Ensure content area has minimum height */
.content-area {
  min-height: calc(100vh - 56px - 48px - env(safe-area-inset-bottom, 0) - env(safe-area-inset-top, 0));
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* Event container styles */
.event-container {
  display: flex;
  flex-direction: column;
  min-height: calc(100vh - 56px - 48px - env(safe-area-inset-bottom, 0) - env(safe-area-inset-top, 0));
  flex: 1;
}

/* Fix for iOS devices */
@supports (-webkit-touch-callout: none) {
  .bottom-navbar-container {
    height: calc(56px + env(safe-area-inset-bottom, 0));
  }

  .mobile-layout-container {
    padding-bottom: calc(56px + env(safe-area-inset-bottom, 0));
  }

  /* Fix for loading state */
  .loading-container {
    min-height: calc(100vh - 56px - 48px - env(safe-area-inset-bottom, 0) - env(safe-area-inset-top, 0));
  }

  /* Fix for map container */
  .map-container {
    position: fixed !important;
    top: calc(48px + env(safe-area-inset-top, 0)) !important;
    bottom: calc(56px + env(safe-area-inset-bottom, 0)) !important;
    left: 0 !important;
    right: 0 !important;
    height: auto !important;
  }
}

/* Special fixes for iOS PWA mode */
.ios-pwa-mode .bottom-navbar-container {
  position: fixed !important;
  bottom: 0 !important;
  left: 0 !important;
  right: 0 !important;
  z-index: 1000 !important;
  height: calc(56px + env(safe-area-inset-bottom, 0)) !important;
  transition: none !important;
  transform: translateZ(0) !important;
  will-change: transform !important;
  -webkit-backface-visibility: hidden !important;
  backface-visibility: hidden !important;
}

.ios-pwa-mode .top-navbar {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  z-index: 1050 !important;
  height: calc(48px + env(safe-area-inset-top, 0)) !important;
  transition: none !important;
  transform: translateZ(0) !important;
  will-change: transform !important;
  -webkit-backface-visibility: hidden !important;
  backface-visibility: hidden !important;
}

/* Fix for map in iOS PWA mode */
.ios-pwa-map {
  position: fixed !important;
  top: calc(48px + env(safe-area-inset-top, 0)) !important;
  bottom: calc(56px + env(safe-area-inset-bottom, 0)) !important;
  left: 0 !important;
  right: 0 !important;
  height: auto !important;
  width: 100% !important;
  margin: 0 !important;
  padding: 0 !important;
  transform: translateZ(0) !important;
  will-change: transform !important;
  -webkit-backface-visibility: hidden !important;
  backface-visibility: hidden !important;
  display: flex !important;
  flex-direction: column !important;
}

/* Ensure the Google Map fills the entire container in iOS PWA mode */
.ios-pwa-map > div {
  flex: 1 !important;
  height: 100% !important;
  min-height: 100% !important;
}

/* Fix for Google Maps container in iOS PWA mode */
.ios-pwa-map .gm-style {
  position: absolute !important;
  height: 100% !important;
  width: 100% !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
}

/* Special container for map in iOS PWA mode */
.ios-pwa-container {
  position: fixed !important;
  top: calc(48px + env(safe-area-inset-top, 0)) !important;
  bottom: calc(56px + env(safe-area-inset-bottom, 0)) !important;
  left: 0 !important;
  right: 0 !important;
  height: auto !important;
  padding: 0 !important;
  margin: 0 !important;
  overflow: hidden !important;
}
