/* Top navbar styling */
.top-navbar {
  background-color: var(--navbar-bg);
  color: var(--navbar-text);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1050; /* Higher than content but lower than dropdown elements */
  height: calc(48px + env(safe-area-inset-top, 0));
  box-shadow: 0 2px 4px var(--shadow-color);
  padding: 0;
  padding-top: env(safe-area-inset-top, 0);
  display: flex;
  align-items: center;
  transition: top 0.3s ease;
  pointer-events: auto; /* Ensure navbar elements are clickable */
}

/* Adjust top navbar for desktop */
@media (min-width: 769px) {
  .top-navbar {
    height: 60px;
    padding-top: 0;
    position: relative; /* Change to relative positioning */
    top: 0;
    left: 0;
    right: 0;
    z-index: 10;
  }

  .navbar-content {
    height: 60px;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    align-items: center;
    justify-content: space-between; /* Distribute items evenly */
    position: relative;
  }

  .navbar-title {
    font-size: 1.2rem;
  }

  .navbar-logo {
    height: 32px;
  }

  /* Desktop navigation links */
  .desktop-nav-links {
    display: flex;
    align-items: center;
    margin-left: auto;
    margin-right: 20px;
    position: relative;
    z-index: 10; /* Higher z-index to ensure links are clickable */
    width: auto;
    min-width: 400px; /* Ensure enough space for all links */
    justify-content: flex-end;
  }

  .desktop-nav-links .nav-link {
    display: flex;
    flex-direction: row;
    align-items: center;
    color: white;
    padding: 8px 16px;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.2s ease;
    border-radius: 4px;
    gap: 8px;
  }

  .desktop-nav-links .nav-link i {
    font-size: 1.1rem;
    transition: transform 0.2s ease;
  }

  .desktop-nav-links .nav-link:hover i {
    transform: scale(1.1);
  }

  .desktop-nav-links .nav-link span {
    font-size: 0.95rem;
  }

  .desktop-nav-links .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
  }

  .desktop-nav-links .nav-link.active {
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 4px;
  }

  .desktop-nav-links .nav-link.active i {
    transform: scale(1.1);
  }

  /* Adjust center logo/title for desktop */
  .navbar-center-desktop {
    position: relative;
    display: flex;
    align-items: center;
    width: auto;
    max-width: 250px;
    justify-content: flex-start;
    margin-right: auto; /* Push to the left */
    margin-left: 15px; /* Space after hamburger menu */
    z-index: 0; /* Lower z-index to ensure menu button is clickable */
  }

  /* Ensure title doesn't overflow */
  .navbar-center-desktop .navbar-title {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 180px; /* Allow space for the logo */
  }
}

.navbar-content {
  display: flex;
  align-items: center;
  padding: 0 10px;
  width: 100%;
  position: relative; /* For absolute positioning of children */
}

.navbar-center {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 0; /* Lower z-index so buttons can be clicked */
  pointer-events: none; /* Make the center area non-interactive so links behind it can be clicked */
}

/* Make the logo and title interactive again */
.navbar-center img,
.navbar-center span {
  pointer-events: auto;
}

.navbar-logo {
  border-radius: 4px;
  background-color: white;
  padding: 2px;
  margin-right: 10px;
  height: 28px;
}

.navbar-title {
  font-size: 1rem;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.navbar-menu-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  position: relative;
  cursor: pointer;
  z-index: 110;
}

.navbar-menu-button i {
  font-size: 1.5rem;
  color: white;
}

.navbar-filter-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  cursor: pointer;
  z-index: 1; /* Higher z-index to be above the centered title */
  margin-left: auto; /* Push to the right */
}

.navbar-filter-button i {
  font-size: 1.25rem;
  color: white;
}

/* Bottom navbar container and styling */
.bottom-navbar-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background-color: var(--navbar-bg);
  box-shadow: 0 -2px 4px var(--shadow-color);
  padding-bottom: env(safe-area-inset-bottom, 0);
  height: auto;
}

.bottom-navbar {
  display: flex;
  justify-content: space-around;
  height: 56px;
  width: 100%;
  color: var(--navbar-text);
}

/* Hide bottom navbar on larger screens */
@media (min-width: 769px) {
  .bottom-navbar-container {
    display: none;
  }

  /* Adjust content padding when bottom navbar is hidden */
  .mobile-layout-container {
    padding-bottom: 0 !important;
  }
}

.bottom-navbar .nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
  color: rgba(255, 255, 255, 0.7);
  text-decoration: none;
  padding: 0;
  position: relative;
  box-sizing: border-box;
  height: 100%;
  margin: 0 2px;
  transition: all 0.2s ease;
}

.bottom-navbar .nav-item i {
  font-size: 1.25rem;
  margin-bottom: 4px;
  transition: transform 0.2s ease;
}

.bottom-navbar .nav-item.active i {
  transform: scale(1.1);
}

.bottom-navbar .nav-item span {
  font-size: 0.75rem;
  line-height: 1;
}

.bottom-navbar .nav-item.active {
  color: white;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  box-shadow: 0 0 10px rgba(255, 255, 255, 0.1);
}

/* Remove the highlight bar */

/* Main menu dropdown */
.main-menu-dropdown {
  position: absolute;
  top: 48px;
  left: 0;
  background-color: var(--dropdown-bg);
  border-radius: 0 0 8px 8px;
  box-shadow: 0 2px 10px var(--shadow-color);
  width: 180px;
  padding: 8px 0;
  z-index: 105; 
  animation: fadeIn 0.2s ease-in-out;
  border: 1px solid var(--dropdown-border);
}

/* Adjust main menu dropdown when offline indicator is shown */
body.has-offline-indicator .main-menu-dropdown {
  top: calc(48px + 36px);
}

.main-menu-dropdown .dropdown-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  color: var(--dropdown-text);
  text-decoration: none;
}

.main-menu-dropdown .dropdown-item i {
  margin-right: 12px;
  font-size: 1rem;
  color: var(--accent);
}

/* Make sure the theme toggle icon is visible */
.main-menu-dropdown .dropdown-item i.bi-sun,
.main-menu-dropdown .dropdown-item i.bi-moon {
  color: var(--accent);
}

.main-menu-dropdown .dropdown-item:hover {
  background-color: var(--dropdown-hover);
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.dropdown-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  color: var(--dropdown-text);
  text-decoration: none;
}

.dropdown-item i {
  margin-right: 12px;
  font-size: 1rem;
  color: var(--accent);
}

.dropdown-item:hover {
  background-color: var(--dropdown-hover);
}

.dropdown-item.active {
  background-color: var(--bg-secondary);
  font-weight: 500;
}

/* Main content padding to accommodate fixed navbars */
.mobile-layout-container {
  padding-top: calc(48px + env(safe-area-inset-top, 0)); /* Height of top navbar + safe area */
  padding-bottom: calc(56px + env(safe-area-inset-bottom, 0)); /* Height of bottom navbar + safe area */
  min-height: 100vh;
  overflow-x: hidden; /* Prevent horizontal scrolling */
  display: flex;
  flex-direction: column;
}

/* Adjust content padding for desktop */
@media (min-width: 769px) {
  /* Mobile layout container styles are not needed for desktop */
  /* Desktop layout container is handled in App.css */
}

/* Compact filter section */
.compact-filters {
  background-color: var(--bg-secondary);
  padding: 10px 12px;
  border-radius: 8px;
  margin-bottom: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0;
  cursor: pointer;
}

.filter-header-content {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 8px;
}

.filter-header h6 {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: #343a40;
}

.active-filters {
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
  font-size: 0.85rem;
  color: #495057;
  background-color: rgba(0, 0, 0, 0.05);
  padding: 2px 8px;
  border-radius: 4px;
  max-width: 200px;
  overflow: hidden;
}

.active-filter-item {
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

.filter-separator {
  color: #adb5bd;
  margin: 0 2px;
}

.filter-content {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.filter-row {
  display: flex;
  width: 100%;
  gap: 12px;
}

.year-filter {
  width: 110px;
  flex-shrink: 0;
}

.location-filter {
  flex-grow: 1;
}

.date-filter {
  flex: 1;
  min-width: 0;
}

.date-filter label {
  display: block;
  margin-bottom: 4px;
  font-size: 0.85rem;
  font-weight: 500;
  color: #495057;
}

.filter-content select,
.filter-content input[type="date"] {
  width: 100%;
  height: 40px;
  padding: 8px 12px;
  font-size: 1rem;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  background-color: white;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.filter-content select {
  appearance: none;
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3E%3Cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m2 5 6 6 6-6'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 0.75rem center;
  background-size: 16px 12px;
  padding-right: 2.5rem;
}

.filter-content select:focus {
  border-color: #80bdff;
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.filter-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 12px;
}

.filter-content button {
  height: 40px;
  padding: 0 16px;
  font-size: 0.9rem;
  background-color: var(--primary-bg);
  color: var(--text-on-primary);
  border: none;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.filter-content button i {
  margin-right: 6px;
}

.filter-content button:hover {
  background-color: var(--primary-darker);
}

/* Mobile warnings filter */
.filter-row.warnings-filter {
  margin-top: 8px;
  margin-bottom: 12px;
  display: flex;
  justify-content: center;
  padding: 0 4px;
}

/* Mobile toggle switch styling */
.filter-row.warnings-filter .warning-toggle {
  position: relative;
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 10px 12px;
  background-color: #fff3cd;
  border-radius: 6px;
  border: 1px solid #ffeeba;
  box-shadow: 0 1px 3px rgba(0,0,0,0.05);
  transition: all 0.2s ease;
  width: 100%;
}

.filter-row.warnings-filter .warning-toggle:hover {
  background-color: #ffecb5;
}

.filter-row.warnings-filter .warning-toggle input {
  position: absolute;
  opacity: 0;
  width: 0;
  height: 0;
}

.filter-row.warnings-filter .warning-toggle-track {
  position: relative;
  display: inline-block;
  width: 36px;
  height: 20px;
  background-color: #e9ecef;
  border-radius: 10px;
  margin-right: 10px;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.filter-row.warnings-filter .warning-toggle input:checked + .warning-toggle-track {
  background-color: var(--accent);
}

.filter-row.warnings-filter .warning-toggle-indicator {
  position: absolute;
  top: 2px;
  left: 2px;
  width: 16px;
  height: 16px;
  background-color: white;
  border-radius: 50%;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.filter-row.warnings-filter .warning-toggle input:checked + .warning-toggle-track .warning-toggle-indicator {
  left: calc(100% - 18px);
}

.filter-row.warnings-filter .warning-toggle-label {
  font-size: 0.85rem;
  color: #495057;
  display: flex;
  align-items: center;
}

.filter-row.warnings-filter .warning-filter-icon {
  margin-right: 6px;
  font-size: 1.1em;
}

.filter-row.warnings-filter .checkmark {
  display: none;
  color: #ffc107;
  font-size: 10px;
  font-weight: bold;
}

.filter-row.warnings-filter .warning-toggle input:checked + .warning-toggle-track .checkmark {
  display: block;
}

/* Collapsible filter section */
.filter-content.collapsed {
  display: none;
}

.filter-toggle {
  transition: transform 0.2s;
  font-size: 1.2rem;
  color: #6c757d;
}

.filter-toggle.open {
  transform: rotate(180deg);
}

/* Desktop navigation links - specific styles */
@media (min-width: 769px) {
  .desktop-nav-links {
    display: flex;
    align-items: center;
    gap: 10px;
    justify-content: center;
  }

  .desktop-nav-links .nav-link {
    color: white;
    padding: 8px 16px;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.2s ease;
    border-radius: 4px;
  }

  .desktop-nav-links .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }

  .desktop-nav-links .nav-link.active {
    background-color: rgba(255, 255, 255, 0.2);
  }
}
