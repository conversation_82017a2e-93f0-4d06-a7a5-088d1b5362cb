import React, { useState, useEffect } from 'react';
import { NavLink, useLocation } from 'react-router-dom';
import axios from 'axios';
import './Navbar.css';
import { useNetworkStatus } from '../contexts/NetworkStatusContext';
import { useTheme } from '../contexts/ThemeContext';

interface NavbarProps {
  onToggleFilters?: () => void;
  showFilterButton?: boolean;
}

const Navbar: React.FC<NavbarProps> = ({ onToggleFilters, showFilterButton = false }) => {
  const [mainMenuOpen, setMainMenuOpen] = useState(false);
  const [isMobile, setIsMobile] = useState(window.innerWidth <= 768);
  const mainMenuRef = React.useRef<HTMLDivElement>(null);
  const location = useLocation();
  const { isOnline, toggleOfflineMode } = useNetworkStatus();
  const { theme, toggleTheme } = useTheme();

  // Check for mobile screen size on mount and resize
  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Handle click outside to close the dropdown
  React.useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (mainMenuRef.current && !mainMenuRef.current.contains(event.target as Node)) {
        setMainMenuOpen(false);
      }
    };

    if (mainMenuOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [mainMenuOpen]);

  const toggleMainMenu = (e: React.MouseEvent) => {
    e.stopPropagation();
    setMainMenuOpen(!mainMenuOpen);
  };

  const closeMainMenu = () => {
    setMainMenuOpen(false);
  };

  const handleLogout = (e: React.MouseEvent) => {
    e.preventDefault();

    // Prevent logout when offline
    if (!isOnline) {
      closeMainMenu();
      return;
    }

    closeMainMenu();
    axios.post('/api/auth/logout')
      .then(() => {
        window.location.href = '/login';
      })
      .catch(error => {
        console.error('Logout failed', error);
      });
  };

  return (
    <>
      {/* Top navbar with pancake menu, title, and filter button */}
      <nav className="top-navbar">
        <div className="navbar-content">
          {/* Left side - Pancake menu */}
          <div ref={mainMenuRef} className="navbar-menu-button" onClick={toggleMainMenu}>
            <i className="bi bi-list"></i>

            {/* Main menu dropdown */}
            {mainMenuOpen && (
              <div className="main-menu-dropdown">
                <a
                  href="#"
                  className="dropdown-item"
                  onClick={(e) => {
                    e.preventDefault();
                    toggleOfflineMode();
                    closeMainMenu();
                  }}
                >
                  <i className={`bi ${isOnline ? 'bi-wifi' : 'bi-wifi-off'}`}></i>
                  <span>{isOnline ? 'Offline Mode' : 'Online Mode'}</span>
                </a>
                <a
                  href="#"
                  className="dropdown-item"
                  onClick={(e) => {
                    e.preventDefault();
                    toggleTheme();
                    closeMainMenu();
                  }}
                >
                  <i className={`bi ${theme === 'dark' ? 'bi-sun' : 'bi-moon'}`}></i>
                  <span>{theme === 'dark' ? 'Light Mode' : 'Dark Mode'}</span>
                </a>
                <a
                  href="#"
                  className={`dropdown-item ${!isOnline ? 'offline-disabled' : ''}`}
                  onClick={handleLogout}
                >
                  <i className="bi bi-box-arrow-right"></i>
                  <span>Logout</span>
                </a>
              </div>
            )}
          </div>

          {/* Left section with app title and logo */}
          {isMobile ? (
            // Mobile: centered title
            <div className="navbar-center">
              <img src="/nannies_120x120.png" alt="Nannies Logo" height="28" className="navbar-logo" />
              <span className="navbar-title">Polish Nannies Schedule</span>
            </div>
          ) : (
            // Desktop: left-aligned title
            <div className="navbar-center-desktop">
              <img src="/nannies_120x120.png" alt="Nannies Logo" height="28" className="navbar-logo" />
              <span className="navbar-title">Polish Nannies Schedule</span>
            </div>
          )}

          {/* Desktop navigation links - only shown in non-mobile mode */}
          {!isMobile && (
            <div className="desktop-nav-links">
              <NavLink
                to="/events"
                className={({ isActive }) => {
                  // Consider both /events and / (root) as active for Events tab
                  const isEventsActive = isActive || location.pathname === '/';
                  return isEventsActive ? "nav-link active" : "nav-link";
                }}
                end
              >
                <i className="bi bi-calendar-event"></i>
                <span>Events</span>
              </NavLink>
              <NavLink
                to="/calendar"
                className={({ isActive }) => isActive ? "nav-link active" : "nav-link"}
              >
                <i className="bi bi-calendar3"></i>
                <span>Calendar</span>
              </NavLink>
              <NavLink
                to="/map"
                className={({ isActive }) => isActive ? "nav-link active" : "nav-link"}
              >
                <i className="bi bi-geo-alt"></i>
                <span>Map</span>
              </NavLink>
              <NavLink
                to="/reminders"
                className={({ isActive }) => isActive ? "nav-link active" : "nav-link"}
              >
                <i className="bi bi-bell"></i>
                <span>Reminders</span>
              </NavLink>
              <NavLink
                to="/analytics"
                className={({ isActive }) => isActive ? "nav-link active" : "nav-link"}
              >
                <i className="bi bi-graph-up"></i>
                <span>Analytics</span>
              </NavLink>
            </div>
          )}

          {/* Right side - Filter button (conditionally shown) */}
          {showFilterButton && isMobile ? (
            <div className="navbar-filter-button" onClick={onToggleFilters}>
              <i className="bi bi-funnel"></i>
            </div>
          ) : (
            /* Empty div to maintain layout balance when filter button is not shown */
            <div style={{ width: '40px', height: '40px' }}></div>
          )}
        </div>
      </nav>
      {/* Bottom navigation bar for mobile only */}
      {isMobile && (
        <div className="bottom-navbar-container">
          <nav className="bottom-navbar">
            <div className="safe-area-spacer"></div>
            <NavLink
              to="/events"
              className={({ isActive }) => {
                // Consider both /events and / (root) as active for Events tab
                const isEventsActive = isActive || location.pathname === '/';
                return isEventsActive ? "nav-item active" : "nav-item";
              }}
            >
              <i className="bi bi-calendar-event"></i>
              <span>Events</span>
            </NavLink>

            <NavLink
              to="/calendar"
              className={({ isActive }) => isActive ? "nav-item active" : "nav-item"}
            >
              <i className="bi bi-calendar3"></i>
              <span>Calendar</span>
            </NavLink>

            <NavLink
              to="/map"
              className={({ isActive }) => isActive ? "nav-item active" : "nav-item"}
            >
              <i className="bi bi-geo-alt"></i>
              <span>Map</span>
            </NavLink>

            <NavLink
              to="/reminders"
              className={({ isActive }) => isActive ? "nav-item active" : "nav-item"}
            >
              <i className="bi bi-bell"></i>
              <span>Reminders</span>
            </NavLink>

            <NavLink
              to="/analytics"
              className={({ isActive }) => isActive ? "nav-item active" : "nav-item"}
            >
              <i className="bi bi-graph-up"></i>
              <span>Analytics</span>
            </NavLink>
          </nav>
        </div>
      )}
    </>
  );
};

export default Navbar;
