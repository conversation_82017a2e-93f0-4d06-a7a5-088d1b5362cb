.offline-indicator {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  padding: 8px 16px;
  text-align: center;
  z-index: 1050;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: background-color 0.3s ease, transform 0.3s ease;
  height: 36px; /* Fixed height for consistent spacing */
  animation: slideDown 0.3s ease-in-out;
}

@keyframes slideDown {
  from { transform: translateY(-100%); }
  to { transform: translateY(0); }
}

.offline-indicator.hiding {
  transform: translateY(-100%);
}

.offline-indicator.offline {
  background-color: var(--danger);
  color: var(--text-on-accent);
}

.offline-indicator.cached-data {
  background-color: var(--warning);
  color: var(--text-dark);
}

/* Add padding to the top of the body when offline indicator is shown */
body.has-offline-indicator {
  padding-top: 36px;
}

/* Adjust navbar position when offline indicator is shown */
body.has-offline-indicator .top-navbar {
  top: 36px;
}

/* Adjust content area when offline indicator is shown */
body.has-offline-indicator .content-area {
  padding-top: 36px;
}

/* Adjust filter menu position when offline indicator is shown */
body.has-offline-indicator .filter-menu {
  top: calc(56px + 36px);
  height: calc(100% - 56px - 36px);
}

/* Mobile specific adjustments */
@media (max-width: 768px) {
  body.has-offline-indicator .bottom-navbar-container {
    bottom: 0;
  }

  body.has-offline-indicator .content-area {
    padding-bottom: 56px;
  }
}

/* Style for disabled buttons in offline mode */
.offline-mode .offline-disabled {
  opacity: 0.6;
  pointer-events: none;
  position: relative;
}

.offline-mode .offline-disabled::after {
  content: "Unavailable offline";
  position: absolute;
  bottom: -20px;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 10px;
  white-space: nowrap;
  opacity: 0;
  transition: opacity 0.2s;
}

.offline-mode .offline-disabled:hover::after {
  opacity: 1;
}
