import React, { useEffect, useState, useRef } from 'react';
import { useNetworkStatus } from '../contexts/NetworkStatusContext';
import './OfflineIndicator.css';

const OfflineIndicator: React.FC = () => {
  const { isOnline, usingCachedData } = useNetworkStatus();
  const shouldBeVisible = !isOnline || usingCachedData;
  const [isVisible, setIsVisible] = useState(shouldBeVisible);
  const [isHiding, setIsHiding] = useState(false);
  const timeoutRef = useRef<number | null>(null);

  // Handle smooth transitions when showing/hiding the indicator
  useEffect(() => {
    if (shouldBeVisible) {
      // Clear any hiding timeout
      if (timeoutRef.current !== null) {
        window.clearTimeout(timeoutRef.current);
        timeoutRef.current = null;
      }
      setIsHiding(false);
      setIsVisible(true);
      document.body.classList.add('has-offline-indicator');
    } else {
      // Start hiding animation
      setIsHiding(true);

      // After animation completes, actually remove from DOM
      timeoutRef.current = window.setTimeout(() => {
        setIsVisible(false);
        document.body.classList.remove('has-offline-indicator');
        timeoutRef.current = null;
      }, 300); // Match the CSS transition duration
    }

    return () => {
      if (timeoutRef.current !== null) {
        window.clearTimeout(timeoutRef.current);
      }
      document.body.classList.remove('has-offline-indicator');
    };
  }, [shouldBeVisible]);

  if (!isVisible) {
    return null;
  }

  return (
    <div className={`offline-indicator ${isOnline ? 'cached-data' : 'offline'} ${isHiding ? 'hiding' : ''}`}>
      {!isOnline ? (
        <>
          <i className="bi bi-wifi-off me-2"></i>
          <span>You're offline. Some features are disabled.</span>
        </>
      ) : (
        <>
          <i className="bi bi-clock-history me-2"></i>
          <span>Showing cached data. Some information may be outdated.</span>
        </>
      )}
    </div>
  );
};

export default OfflineIndicator;
