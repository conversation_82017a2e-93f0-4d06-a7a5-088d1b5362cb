/* PullToRefreshWrapper.css */
.ptr-wrapper {
    width: 100%;
    height: 100%;
    min-height: 100%;
    display: flex;
    flex-direction: column;
}

.ptr-loading-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 10px;
    font-size: 0.9rem;
    color: var(--primary-bg);
}

.ptr-loading-indicator .spin {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* Override some of the library's default styles to match our app theme */
.ptr__pull-down {
    background-color: var(--primary-bg-light) !important;
}

.ptr__children {
    width: 100%;
    height: 100%;
    min-height: 100%;
    display: flex;
    flex-direction: column;
}

/* Make sure the pull-to-refresh container takes full height */
.ptr__pull-down--pull-more,
.ptr__pull-down--active {
    color: var(--primary-bg) !important;
}

.pull-to-refresh-wrapper {
  position: relative;
  width: 100%;
  overflow: hidden;
}

.pull-indicator-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 0;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: flex-end;
  transition: height 0.2s ease-out;
}

.pull-indicator {
  width: 40px;
  height: 40px;
  margin-bottom: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.pull-arrow {
  transform-origin: center;
  transition: transform 0.2s ease-out, opacity 0.2s ease-out;
}

.pull-arrow i {
  font-size: 24px;
  color: #6c757d;
}

.refresh-spinner {
  width: 24px;
  height: 24px;
  border: 3px solid rgba(108, 117, 125, 0.2);
  border-top-color: #6c757d;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.pull-content {
  width: 100%;
  will-change: transform;
}
