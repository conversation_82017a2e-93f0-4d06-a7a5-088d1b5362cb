import React, { useState, useEffect, useRef } from 'react';
import './PullToRefreshWrapper.css';

interface PullToRefreshWrapperProps {
  onRefresh: () => Promise<any>;
  isPullable?: boolean;
  pullDownThreshold?: number;
  maxPullDownDistance?: number;
  children: React.ReactNode;
}

const PullToRefreshWrapper: React.FC<PullToRefreshWrapperProps> = ({
  onRefresh,
  isPullable = true,
  pullDownThreshold = 70,
  maxPullDownDistance = 100,
  children
}) => {
  const [isPulling, setIsPulling] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [pullDistance, setPullDistance] = useState(0);
  const [startY, setStartY] = useState(0);
  const wrapperRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Reset the pull distance when refreshing is done
    if (!isRefreshing) {
      setPullDistance(0);
    }
  }, [isRefreshing]);

  // Add effect to handle non-passive touchmove events
  useEffect(() => {
    const wrapper = wrapperRef.current;
    if (!wrapper) return;
    
    const handleTouchMoveNonPassive = (e: TouchEvent) => {
      if (!isPulling || !isPullable || isRefreshing) return;
      
      const currentY = e.touches[0].clientY;
      const distance = currentY - startY;
      
      // Only allow pulling down, not up
      if (distance > 0) {
        // Apply some resistance for a more natural feel
        const newPullDistance = Math.min(maxPullDownDistance, distance * 0.5);
        setPullDistance(newPullDistance);
        
        // Prevent default scrolling behavior when pulling
        if (distance > 10) {
          e.preventDefault();
        }
      }
    };
    
    // Add non-passive event listener for touchmove
    wrapper.addEventListener('touchmove', handleTouchMoveNonPassive, { passive: false });
    
    return () => {
      // Clean up the event listener
      wrapper.removeEventListener('touchmove', handleTouchMoveNonPassive);
    };
  }, [isPulling, isPullable, isRefreshing, startY, maxPullDownDistance]);

  const handleTouchStart = (e: React.TouchEvent<HTMLDivElement>) => {
    if (!isPullable || isRefreshing) return;
    
    // Only allow pull to refresh when at the top of the page
    const isAtTop = window.scrollY <= 0;
    if (!isAtTop) return;
    
    setStartY(e.touches[0].clientY);
    setIsPulling(true);
  };

  // Keep this function but remove the prevention code since it's now handled in the non-passive listener
  const handleTouchMove = () => {
    // This function is kept for React synthetic event handling
    // The actual preventDefault logic is now in the non-passive event listener
  };

  const handleTouchEnd = async () => {
    if (!isPulling || !isPullable || isRefreshing) return;
    
    setIsPulling(false);
    
    // If pulled far enough, trigger refresh
    if (pullDistance >= pullDownThreshold) {
      setIsRefreshing(true);
      try {
        await onRefresh();
      } catch (error) {
        console.error('Refresh failed:', error);
      } finally {
        setIsRefreshing(false);
        setPullDistance(0);
      }
    } else {
      // Not pulled far enough, reset
      setPullDistance(0);
    }
  };

  // Calculate the progress percentage for the spinner
  const progressPercentage = Math.min(100, (pullDistance / pullDownThreshold) * 100);

  return (
      isPullable && (
        <div 
            className="pull-to-refresh-wrapper"
            ref={wrapperRef}
            onTouchStart={handleTouchStart}
            onTouchMove={handleTouchMove}
            onTouchEnd={handleTouchEnd}
          >
              <div 
              className="pull-indicator-container"
              style={{ height: `${pullDistance}px` }}
            >
              <div className="pull-indicator">
                {isRefreshing ? (
                  <div className="refresh-spinner"></div>
                ) : (
                  <div 
                    className="pull-arrow" 
                    style={{ 
                      transform: progressPercentage >= 100 ? 'rotate(180deg)' : 'rotate(0deg)',
                      opacity: Math.min(1, progressPercentage / 50)
                    }}
                  >
                    <i className="bi bi-arrow-down"></i>
                  </div>
                )}
              </div>
            </div>
            <div 
            className="pull-content" 
            ref={contentRef}
            style={{ 
              transform: `translateY(${pullDistance}px)`,
              transition: isPulling ? 'none' : 'transform 0.2s ease-out'
            }}
          >
            {children}
          </div>
        </div>
      )
      || children
  );
};

export default PullToRefreshWrapper;
