/* Sidebar Filter Menu */
.sidebar-filter-menu {
  width: 350px;
  background-color: var(--bg-main);
  border-left: 1px solid var(--bg-secondary);
  display: flex;
  flex-direction: column;
  height: calc(100vh - 56px);
  position: sticky;
  top: 56px;
  overflow-y: auto;
}

/* Header */
.sidebar-filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid var(--bg-secondary);
}

.sidebar-filter-header h5 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
}

/* Content */
.sidebar-filter-content {
  padding: 16px;
  flex: 1;
  overflow-y: auto;
}

/* Filter Style Overrides */
.sidebar-filter-menu .compact-filters {
  margin: 0;
  padding: 0;
  box-shadow: none;
  border: none;
}

/* Offline Indicator Adjustment */
body.has-offline-indicator .sidebar-filter-menu {
  height: calc(100vh - 56px - 36px);
  top: calc(56px + 36px);
}

/* Mobile Hide */
@media (max-width: 768px) {
  .sidebar-filter-menu {
    display: none;
  }
}
