import React from 'react';
import './SidebarFilterMenu.css';

interface SidebarFilterMenuProps {
  children: React.ReactNode;
}

const SidebarFilterMenu: React.FC<SidebarFilterMenuProps> = ({ children }) => {
  return (
    <div className="sidebar-filter-menu">
      <div className="sidebar-filter-header">
        <h5>Filters</h5>
      </div>
      <div className="sidebar-filter-content">
        {children}
      </div>
    </div>
  );
};

export default SidebarFilterMenu;
