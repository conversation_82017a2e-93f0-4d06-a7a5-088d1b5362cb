import { useState, useEffect } from 'react';
import { useNetworkStatus } from '../contexts/NetworkStatusContext';

interface UpdateNotificationProps {
  onReload: () => void;
}

const UpdateNotification: React.FC<UpdateNotificationProps> = ({ onReload }) => {
  const { isOnline } = useNetworkStatus();
  const [showNotification, setShowNotification] = useState(false);
  const [registration, setRegistration] = useState<ServiceWorkerRegistration | null>(null);
  const [isIOS, setIsIOS] = useState(false);
  const [isPWA, setIsPWA] = useState(false);

  useEffect(() => {
    // Detect iOS
    const isIOSDevice = /iPad|iPhone|iPod/.test(navigator.userAgent) ||
        (navigator.maxTouchPoints > 1 && /MacIntel/.test(navigator.userAgent));
    setIsIOS(isIOSDevice);

    // Detect if running as PWA (standalone mode)
    const isPWAMode = window.matchMedia('(display-mode: standalone)').matches ||
        (window.navigator as any).standalone === true;
    setIsPWA(isPWAMode);

    // Listen for the 'serviceWorkerUpdated' event
    const handleServiceWorkerUpdate = (event: Event) => {
      const customEvent = event as CustomEvent;
      if (customEvent.detail) {
        setRegistration(customEvent.detail);
      }
      setShowNotification(true);
    };

    // Listen for messages from service worker
    const handleMessage = (event: MessageEvent) => {
      if (event.data && event.data.type === 'RELOAD_PAGE') {
        window.location.reload();
      } else if (event.data && event.data.type === 'SERVICE_WORKER_ACTIVATED') {
        console.log('Service worker activated with version:', event.data.version);
        // Force a reload to ensure we're using the latest version
        if (isOnline) {
          window.location.reload();
        }
      }
    };

    window.addEventListener('serviceWorkerUpdated', handleServiceWorkerUpdate);
    navigator.serviceWorker?.addEventListener('message', handleMessage);

    return () => {
      window.removeEventListener('serviceWorkerUpdated', handleServiceWorkerUpdate);
      navigator.serviceWorker?.removeEventListener('message', handleMessage);
    };
  }, []);

  // Handle update click
  const handleUpdate = () => {
    setShowNotification(false);

    // If we have a service worker registration and it's waiting
    if (registration && registration.waiting) {
      // Send message to the service worker to skip waiting
      registration.waiting.postMessage({ type: 'SKIP_WAITING' });

      // For iOS PWA, we need a more aggressive approach
      if (isIOS && isPWA) {
        // Send a message to force refresh all caches
        navigator.serviceWorker.controller?.postMessage({ type: 'FORCE_REFRESH' });
      }
    }

    // Clear API cache when updating to ensure fresh data
    if (navigator.serviceWorker.controller) {
      navigator.serviceWorker.controller.postMessage({ type: 'CLEAR_API_CACHE' });
    }

    // Call the onReload function from props
    onReload();
  };

  // Don't show update notification when offline
  if (!showNotification || !isOnline) {
    return null;
  }

  // Adjust notification position for iOS PWA
  const notificationStyle = isIOS && isPWA
    ? { bottom: 'calc(56px + env(safe-area-inset-bottom, 0) + 10px)' }
    : { bottom: '20px' };

  return (
    <div className="update-notification" style={notificationStyle}>
      <div className="update-notification-content">
        <p>A new version of the app is available!</p>
        <button
          className="btn btn-primary"
          onClick={handleUpdate}
        >
          Update Now
        </button>
      </div>
      <style>
        {`
        .update-notification {
          position: fixed;
          right: 20px;
          background-color: #2c3e50;
          color: white;
          padding: 15px;
          border-radius: 5px;
          box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
          z-index: 1000;
          max-width: 300px;
        }
        .update-notification-content {
          display: flex;
          flex-direction: column;
          align-items: center;
        }
        .update-notification p {
          margin-bottom: 10px;
        }
        `}
      </style>
    </div>
  );
};

export default UpdateNotification;
