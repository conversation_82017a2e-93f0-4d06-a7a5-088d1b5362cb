import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useLoadScript, Libraries } from '@react-google-maps/api';
import BandCalendarApiService from '../services/BandCalendarApiService';

// Define the libraries we want to load
const libraries: Libraries = ['marker'];

// Define the context type
interface GoogleMapsContextType {
  isLoaded: boolean;
  loadError: Error | undefined;
  mapsApiKey: string;
}

// Create the context with undefined as default value
const GoogleMapsContext = createContext<GoogleMapsContextType | undefined>(undefined);

// Provider props interface
interface GoogleMapsProviderProps {
  children: ReactNode;
}

export const GoogleMapsProvider: React.FC<GoogleMapsProviderProps> = ({ children }) => {
  const [mapsApiKey, setMapsApiKey] = useState<string>('');
  const [isInitializing, setIsInitializing] = useState<boolean>(true);
  const [initError, setInitError] = useState<string | null>(null);

  // Load Maps API key on component mount
  useEffect(() => {
    const getApiKey = async () => {
      try {
        const apiKey = await BandCalendarApiService.getMapsApiKey();
        setMapsApiKey(apiKey);
      } catch (err) {
        console.error('Error getting Maps API key:', err);
        setInitError('Failed to load Maps API key. Please try again later.');
      } finally {
        setIsInitializing(false);
      }
    };

    getApiKey();
  }, []);

  // Use the hook to load the script once we have the API key
  const { isLoaded, loadError } = useLoadScript({
    googleMapsApiKey: mapsApiKey,
    libraries: libraries,
  });

  // Log any load errors
  useEffect(() => {
    if (loadError) {
      console.error("Failed to load Google Maps", loadError);
    }
  }, [loadError]);

  // Combine our initialization state with the script loading state
  const contextValue: GoogleMapsContextType = {
    isLoaded: isLoaded && !isInitializing,
    loadError: loadError || (initError ? new Error(initError) : undefined),
    mapsApiKey
  };

  return (
    <GoogleMapsContext.Provider value={contextValue}>
      {children}
    </GoogleMapsContext.Provider>
  );
};

// Custom hook to use the Google Maps context
export const useGoogleMaps = (): GoogleMapsContextType => {
  const context = useContext(GoogleMapsContext);
  if (context === undefined) {
    throw new Error('useGoogleMaps must be used within a GoogleMapsProvider');
  }
  return context;
};
