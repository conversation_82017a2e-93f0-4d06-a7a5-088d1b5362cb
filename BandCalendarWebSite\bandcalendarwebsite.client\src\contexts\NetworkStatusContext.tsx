import React, { createContext, useContext, useState, useEffect, ReactNode, useCallback } from 'react';

interface NetworkStatusContextType {
  isOnline: boolean;
  lastOnlineTime: Date | null;
  usingCachedData: boolean;
  cachedUrls: string[];
  checkOfflineCache: () => void;
  toggleOfflineMode: () => void;
}

const NetworkStatusContext = createContext<NetworkStatusContextType | undefined>(undefined);

export const NetworkStatusProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [isOnline, setIsOnline] = useState<boolean>(navigator.onLine);
  const [lastOnlineTime, setLastOnlineTime] = useState<Date | null>(isOnline ? new Date() : null);
  const [usingCachedData, setUsingCachedData] = useState<boolean>(false);
  const [cachedUrls, setCachedUrls] = useState<string[]>([]);

  // Function to check if we should use offline cache - memoized with useCallback
  const checkOfflineCache = useCallback(() => {
    const useOfflineCache = localStorage.getItem('useOfflineCache') === 'true';
    if (useOfflineCache) {
      setIsOnline(false);
      setUsingCachedData(true);
      // Clear the flag after using it
      localStorage.removeItem('useOfflineCache');
    }
  }, []);

  // Function to toggle offline mode
  const toggleOfflineMode = useCallback(() => {
    if (isOnline) {
      // Going offline
      localStorage.setItem('useOfflineCache', 'true');
      setIsOnline(false);
      setUsingCachedData(true);
    } else {
      // Going online
      localStorage.removeItem('useOfflineCache');
      setIsOnline(navigator.onLine);
      setUsingCachedData(false);
      setCachedUrls([]);
      if (navigator.onLine) {
        setLastOnlineTime(new Date());
      }
    }
  }, [isOnline]);

  useEffect(() => {
    const handleOnline = () => {
      setIsOnline(true);
      setLastOnlineTime(new Date());
      // Don't reset usingCachedData here - we might still be showing cached data
    };

    const handleOffline = () => {
      setIsOnline(false);
    };

    const handleAppOffline = () => {
      setIsOnline(false);
    };

    const handleOfflineDataUsed = (event: Event) => {
      const customEvent = event as CustomEvent;
      setUsingCachedData(true);

      if (customEvent.detail?.url) {
        setCachedUrls(prev => {
          if (!prev.includes(customEvent.detail.url)) {
            return [...prev, customEvent.detail.url];
          }
          return prev;
        });
      }
    };

    // Add event listeners
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);
    window.addEventListener('app-offline', handleAppOffline);
    window.addEventListener('offline-data-used', handleOfflineDataUsed);

    // Clean up event listeners
    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
      window.removeEventListener('app-offline', handleAppOffline);
      window.removeEventListener('offline-data-used', handleOfflineDataUsed);
    };
  }, []);

  // Reset cached data state when coming back online and refreshing data
  useEffect(() => {
    if (isOnline) {
      // Listen for service worker messages
      const handleServiceWorkerMessage = (event: MessageEvent) => {
        if (event.data && event.data.type === 'API_CACHE_CLEARED') {
          setUsingCachedData(false);
          setCachedUrls([]);
        }
      };

      navigator.serviceWorker?.addEventListener('message', handleServiceWorkerMessage);

      return () => {
        navigator.serviceWorker?.removeEventListener('message', handleServiceWorkerMessage);
      };
    }
  }, [isOnline]);

  // Check for offline cache flag on mount
  useEffect(() => {
    checkOfflineCache();
  }, [checkOfflineCache]);

  return (
    <NetworkStatusContext.Provider value={{ 
      isOnline, 
      lastOnlineTime, 
      usingCachedData, 
      cachedUrls, 
      checkOfflineCache,
      toggleOfflineMode 
    }}>
      {children}
    </NetworkStatusContext.Provider>
  );
};

export const useNetworkStatus = (): NetworkStatusContextType => {
  if (NetworkStatusContext === undefined) {
    throw new Error('NetworkStatusContext is undefined');
  }
  const context = useContext(NetworkStatusContext);
  if (context === undefined) {
    throw new Error('useNetworkStatus must be used within a NetworkStatusProvider');
  }
  return context;
};
