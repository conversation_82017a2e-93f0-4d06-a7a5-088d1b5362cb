import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

// Define the theme type
export type ThemeMode = 'light' | 'dark';

// Define the context interface
interface ThemeContextType {
  theme: ThemeMode;
  toggleTheme: () => void;
  setTheme: (theme: ThemeMode) => void;
}

// Create the context with default values
const ThemeContext = createContext<ThemeContextType>({
  theme: 'light',
  toggleTheme: () => {},
  setTheme: () => {},
});

// Custom hook to use the theme context
export const useTheme = () => useContext(ThemeContext);

// Theme provider component
interface ThemeProviderProps {
  children: ReactNode;
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  // Initialize theme from localStorage or system preference
  const [theme, setThemeState] = useState<ThemeMode>(() => {
    // Check if theme is stored in localStorage
    const savedTheme = localStorage.getItem('theme') as ThemeMode;
    if (savedTheme && (savedTheme === 'light' || savedTheme === 'dark')) {
      return savedTheme;
    }

    // If no saved preference, use system preference
    if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
      return 'dark';
    }

    // Default to light mode
    return 'light';
  });

  // Update the DOM when theme changes
  useEffect(() => {
    // Save to localStorage
    localStorage.setItem('theme', theme);

    // Update the data-theme attribute on the document element
    document.documentElement.setAttribute('data-theme', theme);

    // Also add/remove a class for additional styling hooks
    if (theme === 'dark') {
      document.body.classList.add('dark-mode');

      // Update theme-color meta tag for dark mode
      const themeColorMeta = document.querySelector('meta[name="theme-color"]');
      if (themeColorMeta) {
        themeColorMeta.setAttribute('content', '#2c5a7a'); // Dark mode primary color
      }
    } else {
      document.body.classList.remove('dark-mode');

      // Reset theme-color meta tag for light mode
      const themeColorMeta = document.querySelector('meta[name="theme-color"]');
      if (themeColorMeta) {
        themeColorMeta.setAttribute('content', '#4a95b0'); // Light mode primary color
      }
    }
  }, [theme]);

  // Function to toggle between light and dark mode
  const toggleTheme = () => {
    setThemeState(prevTheme => (prevTheme === 'light' ? 'dark' : 'light'));
  };

  // Function to set a specific theme
  const setTheme = (newTheme: ThemeMode) => {
    setThemeState(newTheme);
  };

  // Provide the theme context to children
  return (
    <ThemeContext.Provider value={{ theme, toggleTheme, setTheme }}>
      {children}
    </ThemeContext.Provider>
  );
};
