import { useEffect, useRef } from 'react';
import { createRoot } from 'react-dom/client';

/**
 * Custom hook to render React components into a DOM container (typically for filters)
 * 
 * @param renderComponent - Function that returns the React component to render
 * @param dependencies - Array of dependencies that should trigger re-rendering
 * @param containerId - ID of the DOM element where the component should be rendered (default: 'filter-container')
 * @param isMobile - Whether the component is in mobile mode (affects timing and event handling)
 */
export const useFilterRenderer = (
    renderComponent: () => React.ReactElement,
    dependencies: React.DependencyList,
    containerId: string = 'filter-container'
) => {
    // Reference to store the React root for cleanup
    const rootRef = useRef<ReturnType<typeof createRoot> | null>(null);

    useEffect(() => {
        let isComponentMounted = true;

        const renderFilterContent = () => {
            if (!isComponentMounted) return;

            const filterContainer = document.getElementById(containerId);
            if (!filterContainer) return;

            // Safely unmount the previous root if it exists
            if (rootRef.current) {
                try {
                    rootRef.current.unmount();
                    rootRef.current = null;
                } catch (e) {
                    console.error('Error unmounting previous root:', e);
                }
            }
            // Create a div element to serve as the root container
            const rootDiv = document.createElement('div');
            filterContainer.innerHTML = '';
            filterContainer.appendChild(rootDiv);

            // Create a new root and render the component
            const root = createRoot(rootDiv);
            root.render(renderComponent());
            rootRef.current = root;
        };

        // Initial render - delay slightly to avoid race conditions
        setTimeout(renderFilterContent, 0);

        // Handle filter menu opened event (for mobile views)
        const handleFilterMenuOpened = () => {
            renderFilterContent();
        };

        window.addEventListener('filter-menu-opened', handleFilterMenuOpened);
        // Cleanup function
        return () => {
            isComponentMounted = false;
            window.removeEventListener('filter-menu-opened', handleFilterMenuOpened);

            // Safely unmount the root when component unmounts
            if (rootRef.current) {
                // Use setTimeout to avoid unmounting during render
                setTimeout(() => {
                    try {
                        if (rootRef.current) {
                            rootRef.current.unmount();
                            rootRef.current = null;
                        }
                    } catch (e) {
                        console.error('Error unmounting root during cleanup:', e);
                    }
                }, 0);
            }
        };
    }, dependencies);
};