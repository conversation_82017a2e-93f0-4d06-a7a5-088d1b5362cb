:root {
    font-family: system-ui, Avenir, Helvetica, Arial, sans-serif;
    line-height: 1.5;
    font-weight: 400;
    font-synthesis: none;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

a {
    font-weight: 500;
    color: var(--accent);
    text-decoration: inherit;
}

    a:hover {
        color: var(--accent-darker);
    }

body {
    font-family: Arial, sans-serif;
    padding-top: 0;
    margin-top: 0;
    line-height: 1.6;
    min-width: 320px;
    min-height: 100vh;
    background-color: var(--bg-main);
    color: var(--text-dark);
    transition: background-color 0.3s ease, color 0.3s ease;
}

h1 {
    font-size: 3.2em;
    line-height: 1.1;
    color: var(--text-dark);
    text-align: center;
    margin-bottom: 30px;
}

h2 {
    color: var(--text-dark);
    padding-bottom: 10px;
    margin: 0;
    flex-grow: 1;
}

button {
    border-radius: 8px;
    border: 1px solid transparent;
    padding: 0.6em 1.2em;
    font-size: 1em;
    font-weight: 500;
    font-family: inherit;
    background-color: var(--primary-bg);
    color: var(--text-on-primary);
    cursor: pointer;
    transition: border-color 0.25s, background-color 0.25s;
    display: inline-flex; /* Ensure content is properly aligned */
    align-items: center;
    justify-content: center;
}

    button:hover {
        background-color: var(--primary-darker);
        border-color: var(--primary-darker);
    }

    button:focus,
    button:focus-visible {
        outline: 4px auto -webkit-focus-ring-color;
    }

.summary {
    margin: 0 0 8px 0;
    color: var(--text-medium);
    text-align: center;
    padding: 4px 0;
    font-weight: 500;
    font-size: 0.9rem;
}

.location-badge {
    display: inline-block;
    background-color: var(--bg-secondary);
    color: var(--accent);
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 0.9em;
    font-weight: 500;
    margin-left: 4px;
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    vertical-align: middle;
}

/* Filter controls */
.filters label {
    display: block;
    margin-bottom: 3px;
    font-weight: 500;
    color: var(--text-medium);
    font-size: 0.9rem;
}

.filters select {
    font-size: 0.9rem;
    padding: 8px 12px;
    border-radius: 6px;
    border-color: var(--neutral-lighter);
    background-color: var(--card-bg);
    color: var(--text-dark);
    appearance: none;
    background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3E%3Cpath fill='none' stroke='%23ffffff' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m2 5 6 6 6-6'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 0.75rem center;
    background-size: 16px 12px;
    padding-right: 2rem;
}

[data-theme="light"] .filters select {
    background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3E%3Cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m2 5 6 6 6-6'/%3E%3C/svg%3E");
}

.filters button {
    padding: 8px 16px;
    height: 38px;
    font-size: 0.9rem;
    background-color: var(--primary-bg);
    border-color: var(--primary-bg);
    border-radius: 6px;
    color: var(--text-on-primary);
    transition: background-color 0.25s;
    display: flex;
    align-items: center;
    gap: 6px;
    white-space: nowrap;
    flex-shrink: 0;
}

.filters button:hover {
    background-color: var(--primary-darker);
}

.filters button i {
    font-size: 1rem;
}

/* Venue Breakdown Table Styles */
.venue-breakdown {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 12px var(--shadow-color);
}

    .venue-breakdown thead tr {
        background-color: var(--primary-bg);
        color: var(--text-on-primary);
        font-weight: 600;
        text-align: left;
    }

    .venue-breakdown th,
    .venue-breakdown td {
        padding: 12px 16px;
        border-bottom: 1px solid var(--neutral-lighter);
        color: var(--text-dark);
    }

    .venue-breakdown tbody tr:nth-child(even) {
        background-color: var(--bg-secondary);
    }

    .venue-breakdown tbody tr:hover {
        background-color: var(--bg-tertiary);
    }

    .venue-breakdown tbody tr:last-child td {
        border-bottom: none;
    }

/* Venue count badge */
.venue-count {
    background-color: var(--accent);
    color: var(--text-on-accent);
    border-radius: 20px;
    padding: 4px 10px;
    font-size: 0.9em;
    font-weight: 600;
    display: inline-block;
    min-width: 28px;
    text-align: center;
}

/* Bar chart visualization */
.bar-chart-cell {
    display: flex;
    align-items: center;
    gap: 10px;
}

.bar-container {
    flex: 1;
    background-color: var(--bg-secondary);
    border-radius: 4px;
    overflow: hidden;
    height: 12px;
}

.bar {
    height: 100%;
    background-color: var(--accent);
    transition: width 0.5s ease-out;
}

.venue-name {
    overflow-wrap: break-word;
    word-wrap: break-word;
    word-break: break-word;
    hyphens: auto;
    max-width: 100%;
}

/* Theme transition effects */
* {
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
}
.navbar {
    margin-bottom: 5px;
}

.nav-link.active {
    font-weight: bold;
}

/* Enhanced filter styles */
.filters {
    margin-bottom: 12px;
    background-color: var(--bg-secondary);
    padding: 15px;
    border-radius: 8px;
}

.filter-grid {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    flex-wrap: wrap;
}

.filter-section {
    display: flex;
}

.filter-section select {
    padding: 8px 12px;
    border: 1px solid var(--neutral-lighter);
    border-radius: 6px;
    background-color: var(--card-bg);
    color: var(--text-dark);
    font-size: 0.9rem;
    min-width: 120px;
    appearance: none;
    background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3E%3Cpath fill='none' stroke='%23ffffff' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m2 5 6 6 6-6'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 0.75rem center;
    background-size: 16px 12px;
    padding-right: 2rem;
}

[data-theme="light"] .filter-section select {
    background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3E%3Cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m2 5 6 6 6-6'/%3E%3C/svg%3E");
}

.warning-section, .past-events-section {
    display: flex;
    align-items: center;
    flex: 1;
    justify-content: flex-start;
}

.availability-section {
}

.pill-checkbox-label {
    display: flex;
    align-items: center;
    cursor: pointer;
    user-select: none;
    justify-content: flex-start;
    text-align: left;
    padding: 8px 0;
    position: relative;
}

/* Toggle switch styles */
.pill-checkbox-label {
    position: relative;
    display: inline-flex;
    align-items: center;
    cursor: pointer;
    padding: 6px 12px;
    border-radius: 20px;
    width: 100%;
    background-color: var(--bg-secondary);
    color: var(--text-dark);
}

.pill-checkbox-label input[type="checkbox"] {
    opacity: 0;
    width: 0;
    height: 0;
    position: absolute;
}

.toggle-switch {
    position: relative;
    display: inline-block;
    width: 40px;
    height: 20px;
    background-color: var(--neutral-light);
    border-radius: 20px;
    transition: all 0.3s;
    margin-right: 10px;
    flex-shrink: 0;
    vertical-align: middle;
}

.toggle-switch:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    left: 2px;
    bottom: 2px;
    background-color: white;
    border-radius: 50%;
    transition: all 0.3s;
}

.pill-checkbox-label input:checked + .toggle-switch {
    background-color: var(--primary-bg);
}

.pill-checkbox-label input:checked + .toggle-switch:before {
    transform: translateX(20px);
}

.pill-icon {
    margin-right: 8px;
    font-size: 1.1em;
    vertical-align: middle;
    display: inline-flex;
    align-items: center;
}

.warning-section .pill-icon {
    color: var(--accent);
}

.past-events-section .pill-icon {
    color: var(--primary-bg);
}

.pill-text {
    font-size: 0.9rem;
    color: inherit;
    white-space: nowrap;
    vertical-align: middle;
    display: inline-flex;
    align-items: center;
}

.button-section {
    display: flex;
    align-items: flex-end;
}

.refresh-button {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 8px 16px;
    background-color: var(--primary-bg);
    color: var(--text-on-primary);
    border: none;
    border-radius: 6px;
    font-size: 0.9rem;
    height: 38px;
    cursor: pointer;
    white-space: nowrap;
}

.refresh-button:hover {
    background-color: var(--primary-darker);
}

/* Responsive adjustments for smaller screens */
@media (max-width: 768px) {
    /* Filter layout styles */
    .filter-grid {
        flex-direction: column;
        gap: 10px;
    }

    .filter-section {
        width: 100%;
    }

    .filter-section select {
        width: 100%;
    }

    .warning-section, .past-events-section {
        justify-content: flex-start;
        align-items: flex-start;
    }

    .pill-checkbox-label {
        margin-left: 0;
    }

    .warning-section .pill-checkbox-label,
    .past-events-section .pill-checkbox-label {
        width: auto;
        display: inline-flex;
        align-items: center;
    }

    .toggle-switch {
        margin-left: 0;
    }

    .pill-icon {
        margin-left: 0;
    }
}

/* Style for truncated text in dropdowns */
.filters select option {
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    background-color: var(--card-bg);
    color: var(--text-dark);
}
/* Updated styles for total row at the top */
.table-summary-row {
    border-bottom: 2px solid var(--primary-bg);
    background-color: var(--bg-secondary);
    font-weight: bold;
    order: -1; /* Negative order to appear first in flex layout */
}

/* Special styling for thead when total row is at the top */
.venue-breakdown thead.with-summary tr {
    border-top: none;
}

/* Slightly different appearance for the total count badge */
.total-count {
    background-color: var(--primary-bg);
    font-size: 1.1em;
}

/* Status indicators for reminders */
.reminder-status {
    display: inline-block;
    padding: 4px 10px;
    border-radius: 20px;
    font-size: 0.85em;
    font-weight: 500;
    text-align: center;
}

.reminder-exists {
    background-color: var(--bg-secondary);
    color: var(--text-medium);
}

.reminder-created {
    background-color: rgba(45, 175, 85, 0.2);
    color: var(--success);
}

[data-theme="dark"] .reminder-created {
    background-color: rgba(45, 175, 85, 0.3);
}

/* Action buttons in tables */
.table-action-button {
    padding: 0.4em 0.8em;
    font-size: 0.9em;
}

/* Venue Map Styles */
.venue-map-container {
    padding: 15px;
    background-color: var(--bg-secondary);
    border-radius: 8px;
    margin-bottom: 20px;
}

.venue-map-container h2 {
    margin-bottom: 10px;
    font-size: 1.5rem;
}

/* InfoWindow Styles */
.venue-info {
    padding: 5px;
    min-width: 200px;
}

.venue-info-title {
    margin: 0 0 8px 0;
    font-size: 1.1rem;
    color: var(--primary-bg);
    border-bottom: 1px solid var(--neutral-lighter);
    padding-bottom: 5px;
}

.venue-info-count {
    margin: 0 0 10px 0;
    font-size: 0.9rem;
    color: var(--text-medium);
}

.venue-upcoming-events {
    margin: 10px 0;
    font-size: 0.85rem;
}

.venue-upcoming-label {
    font-weight: bold;
    margin-bottom: 5px;
    color: var(--text-medium);
}

.venue-event-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.venue-event-item {
    padding: 5px 0;
    border-bottom: 1px dotted var(--neutral-lighter);
    display: flex;
    flex-direction: column;
}

.venue-event-item:last-child {
    border-bottom: none;
}

.venue-event-date {
    color: var(--text-light);
    font-size: 0.8rem;
}

.venue-event-title {
    font-weight: 500;
    color: var(--text-dark);
}

.venue-view-button {
    margin-top: 10px;
    width: 100%;
    background-color: var(--primary-bg);
    color: var(--text-on-primary);
}

/* Filter actions styles */
.filter-actions {
    border-bottom: none;
    margin-top: 10px;
}

/* Mobile filter row styles */
.filter-row {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    width: 100%;
    text-align: left;
    margin-bottom: 5px;
}

.filter-row.warnings-filter,
.filter-row.past-events-filter {
    justify-content: flex-start;
    padding-left: 0;
    margin-left: 0;
}

/* App layout with sidebar */
.app-layout {
    display: flex;
    flex-direction: row;
    flex: 1;
}

.app-layout > div:first-child {
    flex: 1;
}

/* Hide sidebar on mobile */
@media (max-width: 768px) {
    .app-layout {
        flex-direction: column;
    }
}
/* Three-way availability toggle styles */
.availability-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.75rem;
    font-size: 0.9rem;
    font-weight: bold;
    color: var(--text-dark);
}

.availability-icon {
    font-size: 1rem;
}

.availability-toggle-group {
    display: flex;
    border: 1px solid #ddd;
    border-radius: 6px;
    overflow: hidden;
    width: 100%;
    background: #f8f9fa;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.availability-toggle-button {
    flex: 1;
    padding: 0.75rem 1rem;
    border: none;
    background: transparent;
    color: #6c757d;
    font-weight: 500;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    border-right: 1px solid #ddd;
}

.availability-toggle-button:last-child {
    border-right: none;
}

.availability-toggle-button:hover:not(.active) {
    background: #e9ecef;
    color: #495057;
}

.availability-toggle-group .availability-toggle-button.active {
    background: #4a95b0 !important;
    color: white !important;
    font-weight: 600;
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Dark mode support for three-way toggle */
[data-theme="dark"] .availability-label {
    color: #f7fafc;
}

[data-theme="dark"] .availability-toggle-group {
    border-color: #4a5568;
    background: #2d3748;
}

[data-theme="dark"] .availability-toggle-button {
    color: #a0aec0;
    border-right-color: #4a5568;
}

[data-theme="dark"] .availability-toggle-button:hover:not(.active) {
    background: #4a5568;
    color: #e2e8f0;
}

[data-theme="dark"] .availability-toggle-group .availability-toggle-button.active {
    background: #4299e1 !important;
    color: white !important;
}

/* Mobile responsive for three-way toggle */
@media (max-width: 768px) {
    .availability-toggle-button {
        padding: 0.65rem 0.75rem;
        font-size: 0.85rem;
    }
    
    .availability-label {
        font-size: 0.85rem;
        margin-bottom: 0.5rem;
    }
}

