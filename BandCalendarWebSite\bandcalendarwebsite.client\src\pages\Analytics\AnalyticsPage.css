/* Analytics Component Styles */

/* Card styling */
.card-body {
    padding: 1rem;
}

@media (max-width: 768px) {
    .card-body {
        padding: 0.75rem;
    }
}

/* Tab styling */
.nav-tabs {
    border-bottom: 1px solid var(--bg-secondary);
    display: flex;
    flex-wrap: nowrap;
    width: 100%;
}

.nav-tabs .nav-item {
    flex: 1;
    text-align: center;
}

.nav-tabs .nav-link {
    margin-bottom: -1px;
    border: 1px solid transparent;
    border-top-left-radius: 0.25rem;
    border-top-right-radius: 0.25rem;
    color: var(--text-medium);
    background-color: transparent;
    border-color: transparent;
    cursor: pointer;
    padding: 0.5rem 0.75rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
}

.nav-tabs .nav-link:hover {
    border-color: var(--bg-secondary);
    color: var(--accent);
}

.nav-tabs .nav-link.active {
    color: var(--primary-bg);
    background-color: var(--bg-main);
    border-color: var(--bg-secondary) var(--bg-secondary) var(--bg-main);
    font-weight: 500;
}

/* Tab content transitions */
.tab-content > .tab-pane {
    display: none;
}

.tab-content > .active {
    display: block;
}

.tab-content > .fade {
    transition: opacity 0.15s linear;
}

.tab-content > .fade.show {
    opacity: 1;
}

/* Venue name and count styling */
.venue-name {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 100%;
}

/* Mobile card view styles */
@media (max-width: 768px) {
    .nav-tabs .nav-link {
        font-size: 0.9rem;
        padding: 0.5rem;
    }

    .mobile-card-view {
        display: flex;
        flex-direction: column;
        gap: 8px;
    }

    .mobile-card-item {
        background-color: var(--card-bg);
        border-radius: 8px;
        padding: 10px;
        box-shadow: 0 1px 3px var(--shadow-color);
        border-left: 2px solid var(--accent);
        color: var(--text-dark);
    }

    .mobile-card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 6px;
    }

    .mobile-card-title {
        font-weight: 500;
        font-size: 0.95rem;
        flex: 1;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .mobile-card-count {
        background-color: var(--accent);
        color: var(--text-on-accent);
        border-radius: 20px;
        padding: 2px 8px;
        font-size: 0.8rem;
        font-weight: 600;
        margin-left: 8px;
        min-width: 24px;
        text-align: center;
    }

    .mobile-card-footer {
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .mobile-card-bar-container {
        flex: 1;
        height: 10px;
        background-color: var(--bg-secondary);
        border-radius: 6px;
        overflow: hidden;
    }

    .mobile-card-bar-fill {
        height: 100%;
        background-color: var(--accent);
    }

    .mobile-card-percentage {
        font-size: 0.8rem;
        color: var(--text-medium);
        min-width: 30px;
        text-align: right;
    }

    .mobile-card-total {
        background-color: var(--bg-secondary);
        border-left: 3px solid var(--primary-bg);
    }

    .mobile-card-total .mobile-card-title {
        font-weight: 600;
    }

    .mobile-card-total .mobile-card-count {
        background-color: var(--primary-bg);
    }

    .venue-name {
        font-size: 0.9rem;
    }

    .venue-count {
        font-size: 0.85rem;
        padding: 3px 8px;
    }
}
