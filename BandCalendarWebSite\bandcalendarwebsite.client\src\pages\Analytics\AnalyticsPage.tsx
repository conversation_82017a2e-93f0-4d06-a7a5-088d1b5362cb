import React, { useState, useEffect } from 'react';
import BandCalendarApiService, { VenueBreakdown, CalendarEvent } from '../../services/BandCalendarApiService.ts';
import { format } from 'date-fns';
import './AnalyticsPage.css';
import PullToRefreshWrapper from '../../components/PullToRefreshWrapper.tsx';

// Define interface for month breakdown
interface MonthBreakdown {
    [month: string]: number;
}

const AnalyticsPage: React.FC = () => {
    const [year, setYear] = useState(2025); // Default to 2025
    const [gigCount, setGigCount] = useState(0);
    const [venueBreakdown, setVenueBreakdown] = useState<VenueBreakdown>({});
    const [monthBreakdown, setMonthBreakdown] = useState<MonthBreakdown>({});
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [activeTab, setActiveTab] = useState('venue'); // Default to venue tab
    const [isMobile, setIsMobile] = useState(window.innerWidth < 768);

    // Available years for dropdown
    const years = [2025, 2026];

    // Update isMobile state when window is resized
    useEffect(() => {
        const handleResize = () => {
            setIsMobile(window.innerWidth < 768);
        };

        window.addEventListener('resize', handleResize);
        return () => window.removeEventListener('resize', handleResize);
    }, []);

    useEffect(() => {
        fetchAnalytics();
    }, [year]); // Fetch analytics when year changes

    const fetchAnalytics = async () => {
        setLoading(true);
        setError(null);

        try {
            // Create date range for the selected year
            const startDate = format(new Date(year, 0, 1), 'yyyy-MM-dd'); // January 1st
            const endDate = format(new Date(year, 11, 31), 'yyyy-MM-dd'); // December 31st

            // Fetch all gig events for the year
            const allEvents = await BandCalendarApiService.getGigEvents(startDate, endDate, null);
            
            // Filter out availability events - Analytics should only show actual gigs
            const events = allEvents.filter(event => !event.isAvailabilityEvent);

            // Calculate gig count
            const count = events.length;
            setGigCount(count);

            // Calculate venue breakdown
            const venueData = calculateVenueBreakdown(events);
            setVenueBreakdown(venueData);

            // Calculate month breakdown
            const monthData = calculateMonthBreakdown(events);
            setMonthBreakdown(monthData);
        } catch (err) {
            console.error('Error fetching analytics:', err);
            setError('Failed to load analytics. Please try again later.');
        } finally {
            setLoading(false);
        }
    };

    // Calculate venue breakdown from events
    const calculateVenueBreakdown = (events: CalendarEvent[]): VenueBreakdown => {
        const venueMap: VenueBreakdown = {};

        events.forEach(event => {
            const venue = event.location || 'No Venue Listed';
            venueMap[venue] = (venueMap[venue] || 0) + 1;
        });

        return venueMap;
    };

    // Calculate month breakdown from events
    const calculateMonthBreakdown = (events: CalendarEvent[]): MonthBreakdown => {
        const monthMap: MonthBreakdown = {};

        // Initialize all months with zero count
        for (let i = 0; i < 12; i++) {
            const monthName = format(new Date(year, i, 1), 'MMMM');
            monthMap[monthName] = 0;
        }

        // Count events by month
        events.forEach(event => {
            const eventDate = new Date(event.startTime);
            const monthName = format(eventDate, 'MMMM');
            monthMap[monthName] = (monthMap[monthName] || 0) + 1;
        });

        return monthMap;
    };

    const getMaxVenueCount = () => {
        if (Object.keys(venueBreakdown).length === 0) return 1;
        return Math.max(...Object.values(venueBreakdown));
    };

    const getMaxMonthCount = () => {
        if (Object.keys(monthBreakdown).length === 0) return 1;
        return Math.max(...Object.values(monthBreakdown));
    };

    const maxVenueCount = getMaxVenueCount();
    const maxMonthCount = getMaxMonthCount();

    return (
        <div className="container px-2 py-2">
            <PullToRefreshWrapper onRefresh={fetchAnalytics} isPullable={isMobile}>
                <div className="row mb-3">
                    <div className="col-md-4">
                        <div className="mb-2">
                            <div className="d-flex align-items-center gap-2">
                                <label htmlFor="yearSelect" className="form-label mb-0">Year:</label>
                                <select
                                    id="yearSelect"
                                    value={year}
                                    onChange={(e) => setYear(parseInt(e.target.value))}
                                    className="form-select"
                                    style={{ width: 'auto' }}
                                >
                                    {years.map(y => (
                                        <option key={y} value={y}>{y}</option>
                                    ))}
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                {loading && <p className="text-center">Loading...</p>}
                {error && <p className="text-danger text-center">{error}</p>}

            {!loading && !error && (
                <div className="row">
                    <div className="col-12">
                        <div className="card" style={{ backgroundColor: 'var(--card-bg)', color: 'var(--text-dark)' }}>
                            <div className="card-body">
                                {/* Tabs navigation */}
                                <ul className="nav nav-tabs mb-2" role="tablist">
                                    <li className="nav-item" role="presentation">
                                        <button
                                            className={`nav-link ${activeTab === 'venue' ? 'active' : ''}`}
                                            onClick={() => setActiveTab('venue')}
                                            type="button"
                                            role="tab"
                                        >
                                            Venue Breakdown
                                        </button>
                                    </li>
                                    <li className="nav-item" role="presentation">
                                        <button
                                            className={`nav-link ${activeTab === 'month' ? 'active' : ''}`}
                                            onClick={() => setActiveTab('month')}
                                            type="button"
                                            role="tab"
                                        >
                                            Month Breakdown
                                        </button>
                                    </li>
                                </ul>

                                {/* Tab content */}
                                <div className="tab-content">
                                    {/* Venue Breakdown Tab */}
                                    <div className={`tab-pane fade ${activeTab === 'venue' ? 'show active' : ''}`}>
                                        {Object.keys(venueBreakdown).length > 0 ? (
                                            <>
                                                {/* Desktop view */}
                                                <div className="table-responsive d-none d-md-block">
                                                    <table className="venue-breakdown table">
                                                        <thead className="with-summary">
                                                            <tr>
                                                                <th style={{ width: '40%' }}>Venue</th>
                                                                <th style={{ width: '15%' }}>Count</th>
                                                                <th style={{ width: '45%' }}>Distribution</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            <tr className="table-summary-row">
                                                                <td>
                                                                    <div className="venue-name fw-bold">TOTAL</div>
                                                                </td>
                                                                <td>
                                                                    <span className="venue-count total-count">{gigCount}</span>
                                                                </td>
                                                                <td className="bar-chart-cell">
                                                                    <div className="bar-container">
                                                                        <div className="bar" style={{ width: '100%' }}></div>
                                                                    </div>
                                                                    <span>100%</span>
                                                                </td>
                                                            </tr>
                                                            {Object.entries(venueBreakdown)
                                                                .sort(([, countA], [, countB]) => countB - countA)
                                                                .map(([venue, count]) => {
                                                                    // Truncate venue name for display
                                                                    const displayVenue = venue.length > 40
                                                                        ? venue.substring(0, 40) + '...'
                                                                        : venue;

                                                                    return (
                                                                        <tr key={venue}>
                                                                            <td>
                                                                                <div className="venue-name" title={venue}>{displayVenue}</div>
                                                                            </td>
                                                                            <td>
                                                                                <span className="venue-count">{count}</span>
                                                                            </td>
                                                                            <td className="bar-chart-cell">
                                                                                <div className="bar-container">
                                                                                    <div
                                                                                        className="bar"
                                                                                        style={{ width: `${(count / maxVenueCount) * 100}%` }}
                                                                                    ></div>
                                                                                </div>
                                                                                <span>{Math.round((count / gigCount) * 100)}%</span>
                                                                            </td>
                                                                        </tr>
                                                                    );
                                                                })}
                                                        </tbody>
                                                    </table>
                                                </div>

                                                {/* Mobile card view */}
                                                <div className="mobile-card-view d-md-none">
                                                    {/* Total card */}
                                                    <div className="mobile-card-item mobile-card-total">
                                                        <div className="mobile-card-header">
                                                            <div className="mobile-card-title">TOTAL</div>
                                                            <div className="mobile-card-count">{gigCount}</div>
                                                        </div>
                                                        <div className="mobile-card-footer">
                                                            <div className="mobile-card-bar-container">
                                                                <div className="mobile-card-bar-fill" style={{ width: '100%' }}></div>
                                                            </div>
                                                            <div className="mobile-card-percentage">100%</div>
                                                        </div>
                                                    </div>

                                                    {/* Venue cards */}
                                                    {Object.entries(venueBreakdown)
                                                        .sort(([, countA], [, countB]) => countB - countA)
                                                        .map(([venue, count]) => {
                                                            const percentage = Math.round((count / gigCount) * 100);

                                                            return (
                                                                <div className="mobile-card-item" key={venue}>
                                                                    <div className="mobile-card-header">
                                                                        <div className="mobile-card-title" title={venue}>{venue}</div>
                                                                        <div className="mobile-card-count">{count}</div>
                                                                    </div>
                                                                    <div className="mobile-card-footer">
                                                                        <div className="mobile-card-bar-container">
                                                                            <div
                                                                                className="mobile-card-bar-fill"
                                                                                style={{ width: `${(count / maxVenueCount) * 100}%` }}
                                                                            ></div>
                                                                        </div>
                                                                        <div className="mobile-card-percentage">{percentage}%</div>
                                                                    </div>
                                                                </div>
                                                            );
                                                        })}
                                                </div>
                                            </>
                                        ) : (
                                            <p>No venue data available.</p>
                                        )}
                                    </div>

                                    {/* Month Breakdown Tab */}
                                    <div className={`tab-pane fade ${activeTab === 'month' ? 'show active' : ''}`}>
                                        {Object.keys(monthBreakdown).length > 0 ? (
                                            <>
                                                {/* Desktop view */}
                                                <div className="table-responsive d-none d-md-block">
                                                    <table className="venue-breakdown table">
                                                        <thead className="with-summary">
                                                            <tr>
                                                                <th style={{ width: '40%' }}>Month</th>
                                                                <th style={{ width: '15%' }}>Count</th>
                                                                <th style={{ width: '45%' }}>Distribution</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            <tr className="table-summary-row">
                                                                <td>
                                                                    <div className="venue-name fw-bold">TOTAL</div>
                                                                </td>
                                                                <td>
                                                                    <span className="venue-count total-count">{gigCount}</span>
                                                                </td>
                                                                <td className="bar-chart-cell">
                                                                    <div className="bar-container">
                                                                        <div className="bar" style={{ width: '100%' }}></div>
                                                                    </div>
                                                                    <span>100%</span>
                                                                </td>
                                                            </tr>
                                                            {Object.entries(monthBreakdown)
                                                                // Sort by month index (January first)
                                                                .sort(([monthA], [monthB]) => {
                                                                    const monthOrder = [
                                                                        'January', 'February', 'March', 'April', 'May', 'June',
                                                                        'July', 'August', 'September', 'October', 'November', 'December'
                                                                    ];
                                                                    return monthOrder.indexOf(monthA) - monthOrder.indexOf(monthB);
                                                                })
                                                                .map(([month, count]) => (
                                                                    <tr key={month}>
                                                                        <td>
                                                                            <div className="venue-name">{month}</div>
                                                                        </td>
                                                                        <td>
                                                                            <span className="venue-count">{count}</span>
                                                                        </td>
                                                                        <td className="bar-chart-cell">
                                                                            <div className="bar-container">
                                                                                <div
                                                                                    className="bar"
                                                                                    style={{ width: `${(count / maxMonthCount) * 100}%` }}
                                                                                ></div>
                                                                            </div>
                                                                            <span>{gigCount > 0 ? Math.round((count / gigCount) * 100) : 0}%</span>
                                                                        </td>
                                                                    </tr>
                                                                ))}
                                                        </tbody>
                                                    </table>
                                                </div>

                                                {/* Mobile card view */}
                                                <div className="mobile-card-view d-md-none">
                                                    {/* Total card */}
                                                    <div className="mobile-card-item mobile-card-total">
                                                        <div className="mobile-card-header">
                                                            <div className="mobile-card-title">TOTAL</div>
                                                            <div className="mobile-card-count">{gigCount}</div>
                                                        </div>
                                                        <div className="mobile-card-footer">
                                                            <div className="mobile-card-bar-container">
                                                                <div className="mobile-card-bar-fill" style={{ width: '100%' }}></div>
                                                            </div>
                                                            <div className="mobile-card-percentage">100%</div>
                                                        </div>
                                                    </div>

                                                    {/* Month cards */}
                                                    {Object.entries(monthBreakdown)
                                                        // Sort by month index (January first)
                                                        .sort(([monthA], [monthB]) => {
                                                            const monthOrder = [
                                                                'January', 'February', 'March', 'April', 'May', 'June',
                                                                'July', 'August', 'September', 'October', 'November', 'December'
                                                            ];
                                                            return monthOrder.indexOf(monthA) - monthOrder.indexOf(monthB);
                                                        })
                                                        .map(([month, count]) => {
                                                            const percentage = gigCount > 0 ? Math.round((count / gigCount) * 100) : 0;

                                                            return (
                                                                <div className="mobile-card-item" key={month}>
                                                                    <div className="mobile-card-header">
                                                                        <div className="mobile-card-title">{month}</div>
                                                                        <div className="mobile-card-count">{count}</div>
                                                                    </div>
                                                                    <div className="mobile-card-footer">
                                                                        <div className="mobile-card-bar-container">
                                                                            <div
                                                                                className="mobile-card-bar-fill"
                                                                                style={{ width: `${(count / maxMonthCount) * 100}%` }}
                                                                            ></div>
                                                                        </div>
                                                                        <div className="mobile-card-percentage">{percentage}%</div>
                                                                    </div>
                                                                </div>
                                                            );
                                                        })}
                                                </div>
                                            </>
                                        ) : (
                                            <p>No month data available.</p>
                                        )}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            )}
            </PullToRefreshWrapper>
        </div>
    );
};

export default AnalyticsPage;

