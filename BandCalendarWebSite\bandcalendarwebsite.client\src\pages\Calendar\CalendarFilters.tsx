import React from 'react';
import { format } from 'date-fns';

interface CalendarFiltersProps {
    location: string;
    locations: string[];
    onLocationChange: (location: string | React.ChangeEvent<HTMLSelectElement>) => void;
    currentDate: Date;
    onDateChange: (date: Date) => void;
    viewMode: 'month' | 'week';
    onViewModeChange: (mode: 'month' | 'week') => void;
    showAvailability: boolean;
    onAvailabilityToggle: (show: boolean) => void;
    onRefresh: () => void;
}

const CalendarFilters: React.FC<CalendarFiltersProps> = ({
    location,
    locations,
    onLocationChange,
    currentDate,
    onDateChange,
    viewMode,
    onViewModeChange,
    showAvailability,
    onAvailabilityToggle,
    onRefresh
}) => {
    const handleDateInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const newDate = new Date(e.target.value);
        if (!isNaN(newDate.getTime())) {
            onDateChange(newDate);
        }
    };

    return (
        <div className="calendar-filters">
            <div className="filter-row">
                <div className="filter-group">
                    <label htmlFor="calendar-date-input">Date:</label>
                    <input
                        id="calendar-date-input"
                        type="date"
                        value={format(currentDate, 'yyyy-MM-dd')}
                        onChange={handleDateInputChange}
                        className="form-control"
                    />
                </div>

                <div className="filter-group">
                    <label htmlFor="calendar-view-select">View:</label>
                    <select
                        id="calendar-view-select"
                        value={viewMode}
                        onChange={(e) => onViewModeChange(e.target.value as 'month' | 'week')}
                        className="form-control"
                    >
                        <option value="month">Month</option>
                        <option value="week">Week</option>
                    </select>
                </div>
            </div>

            <div className="filter-row">
                <div className="filter-group">
                    <label htmlFor="calendar-location-select">Location:</label>
                    <select
                        id="calendar-location-select"
                        value={location}
                        onChange={onLocationChange}
                        className="form-control"
                    >
                        <option value="">All Locations</option>
                        {locations.map(loc => (
                            <option key={loc} value={loc}>{loc}</option>
                        ))}
                    </select>
                </div>

                <div className="filter-group">
                    <label className="checkbox-label">
                        <input
                            type="checkbox"
                            checked={showAvailability}
                            onChange={(e) => onAvailabilityToggle(e.target.checked)}
                        />
                        Show Availability Events
                    </label>
                </div>
            </div>

            <div className="filter-row">
                <button 
                    className="refresh-button"
                    onClick={onRefresh}
                    aria-label="Refresh calendar"
                >
                    <i className="bi bi-arrow-clockwise"></i> Refresh
                </button>
            </div>
        </div>
    );
};

export default CalendarFilters;
