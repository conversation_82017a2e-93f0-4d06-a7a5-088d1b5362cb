/* Calendar Page Styles */
.calendar-container {
    height: 100%;
    overflow-y: auto;
}

.calendar-content {
    padding: 1rem;
    min-height: 100vh;
}

.calendar-content.loading {
    opacity: 0.7;
    pointer-events: none;
}

.loading-indicator {
    text-align: center;
    padding: 2rem;
    font-size: 1.1rem;
    color: var(--primary-color);
}

.error {
    color: var(--error-color);
    text-align: center;
    padding: 1rem;
    background-color: var(--error-bg-color);
    border-radius: 4px;
    margin-bottom: 1rem;
}

/* Calendar Filters */
.calendar-filters {
    background: var(--card-bg-color);
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
    border: 1px solid var(--border-color);
}

.filter-row {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
    align-items: end;
    flex-wrap: wrap;
}

.filter-row:last-child {
    margin-bottom: 0;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    min-width: 150px;
}

.filter-group label {
    font-weight: 500;
    font-size: 0.9rem;
    color: var(--text-color);
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    flex-direction: row !important;
    cursor: pointer;
    margin-top: 1.5rem;
}

.checkbox-label input[type="checkbox"] {
    margin: 0;
}

.form-control {
    padding: 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background: var(--input-bg-color);
    color: var(--text-color);
    font-size: 0.9rem;
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(var(--primary-color-rgb), 0.2);
}

.refresh-button {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: background-color 0.2s;
}

.refresh-button:hover {
    background: var(--primary-hover-color);
}

/* Mobile responsive adjustments */
@media (max-width: 768px) {
    .calendar-content {
        padding: 0.5rem;
    }
    
    .filter-row {
        flex-direction: column;
        align-items: stretch;
    }
    
    .filter-group {
        min-width: auto;
        width: 100%;
    }
    
    .checkbox-label {
        margin-top: 0.5rem;
    }
}
