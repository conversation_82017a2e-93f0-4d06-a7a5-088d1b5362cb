import React, { useEffect, useState } from 'react';
import { useSearchParams } from 'react-router-dom';
import CalendarView from './CalendarView';
import CalendarFilters from './CalendarFilters';
import PullToRefreshWrapper from '../../components/PullToRefreshWrapper';
import BandCalendarApiService, { CalendarEvent as Event } from '../../services/BandCalendarApiService';
import { useFilterRenderer } from '../../hooks';
import './CalendarPage.css';
import { format } from 'date-fns';

interface CalendarPageProps {
    isMobile?: boolean;
}

const CalendarPage: React.FC<CalendarPageProps> = ({ isMobile = false }) => {
    const [searchParams, setSearchParams] = useSearchParams();
    const [allEvents, setAllEvents] = useState<Event[]>([]);
    //const [currentDate, setCurrentDate] = useState<Date>(new Date());
    const [location, setLocation] = useState(searchParams.get('location') || '');
    const [locations, setLocations] = useState<string[]>([]);
    const [viewMode, setViewMode] = useState<'month' | 'week'>(
        (searchParams.get('view') as 'month' | 'week') || 'month'
    );
    const [showAvailability, setShowAvailability] = useState(
        searchParams.get('availability') !== 'false' // Default to true unless explicitly set to false
    );
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    // Temp hack to get build working without completely ripping currentDate out
    const currentDate = new Date();

    // Effect to handle URL query parameters
    useEffect(() => {
        const locationParam = searchParams.get('location');
        const viewParam = searchParams.get('view') as 'month' | 'week';
        const availabilityParam = searchParams.get('availability');
        //const dateParam = searchParams.get('date');

        if (locationParam !== location) {
            setLocation(locationParam || '');
        }

        if (viewParam && viewParam !== viewMode) {
            setViewMode(viewParam);
        }

        const newAvailabilityValue = availabilityParam !== 'false'; // Default to true unless explicitly false
        if (newAvailabilityValue !== showAvailability) {
            setShowAvailability(newAvailabilityValue);
        }
        /*
        if (dateParam) {
            const parsedDate = new Date(dateParam);
            if (!isNaN(parsedDate.getTime()) && parsedDate.getTime() !== currentDate.getTime()) {
                setCurrentDate(parsedDate);
            }
        }
         */
    }, [searchParams, location, viewMode, showAvailability/*, currentDate*/]);

    // Effect to fetch events when date or view mode changes
    useEffect(() => {
        fetchEvents();
    }, [/*currentDate, */viewMode]);

    const fetchEvents = async () => {
        setLoading(true);
        setError(null);

        try {
            // Fetch events for the date range (now includes both gigs and availability)
            const data = await BandCalendarApiService.getGigEvents(
                '2025-01-01',
                '2026-01-01',
                null
            );
            setAllEvents(data);

            // Extract unique locations from events data (only from gig events)
            const uniqueLocations = data
                .filter(event => !event.isAvailabilityEvent && event.location && event.location.trim() !== '')
                .map(event => event.location)
                .filter((loc, index, self) => self.indexOf(loc) === index)
                .sort();

            setLocations(uniqueLocations);
        } catch (err) {
            console.error('Error fetching events:', err);
            setError('Failed to load events. Please try again later.');
            setAllEvents([]);
        } finally {
            setLoading(false);
        }
    };

    const handleLocationChange = (locationValue: string | React.ChangeEvent<HTMLSelectElement>) => {
        const newLocation = typeof locationValue === 'string'
            ? locationValue
            : locationValue.target.value;

        updateSearchParams(newLocation, currentDate, viewMode, showAvailability);
    };

    const handleDateChange = (newDate: Date) => {
        updateSearchParams(location, newDate, viewMode, showAvailability);
    };

    const handleViewModeChange = (newViewMode: 'month' | 'week') => {
        updateSearchParams(location, currentDate, newViewMode, showAvailability);
    };

    const handleAvailabilityToggle = (showAvail: boolean) => {
        updateSearchParams(location, currentDate, viewMode, showAvail);
    };

    const updateSearchParams = (
        loc: string,
        date: Date,
        view: 'month' | 'week',
        availability: boolean
    ) => {
        const params = new URLSearchParams();

        if (loc) {
            params.set('location', loc);
        }

        params.set('date', format(date, 'yyyy-MM-dd'));
        params.set('view', view);

        if (!availability) {
            params.set('availability', 'false');
        }

        setSearchParams(params);
    };

    useFilterRenderer(
        () => (
            <CalendarFilters
                location={location}
                locations={locations}
                onLocationChange={handleLocationChange}
                currentDate={currentDate}
                onDateChange={handleDateChange}
                viewMode={viewMode}
                onViewModeChange={handleViewModeChange}
                showAvailability={showAvailability}
                onAvailabilityToggle={handleAvailabilityToggle}
                onRefresh={fetchEvents}
            />
        ),
        [isMobile, location, locations, currentDate, viewMode, showAvailability]
    );

    return (
        <div className="calendar-container">
            <PullToRefreshWrapper onRefresh={fetchEvents} isPullable={false}>
                <div className={`calendar-content ${loading ? 'loading' : ''}`}>
                    {loading && <div className="loading-indicator">Loading...</div>}
                    {error && <p className="error">{error}</p>}
                    {!loading && !error && (
                        <CalendarView
                            events={allEvents}
                            isMobile={isMobile}
                            showAvailability={showAvailability}
                            location={location}
                        />
                    )}
                </div>
            </PullToRefreshWrapper>
        </div>
    );
};

export default CalendarPage;
