import React, {useCallback, useMemo} from 'react';
import {Calendar, momentLocalizer, View, Views, EventProps} from 'react-big-calendar';
import moment from 'moment';
import { CalendarEvent, UnavailabilityInfo } from '../../services/BandCalendarApiService';
import 'react-big-calendar/lib/css/react-big-calendar.css';
import './CalendarView.css';

const localizer = momentLocalizer(moment);

interface CalendarViewProps {
    events: CalendarEvent[];
    isMobile?: boolean;
    showAvailability?: boolean;
    location?: string;
}

// Define the event object shape for the calendar
interface CalendarEventType {
    id: string;
    title: string;
    start: Date;
    end: Date;
    allDay: boolean;
    resource: {
        isAvailabilityEvent: boolean;
        location?: string;
        htmlLink?: string;
        summary?: string;
        description?: string;
        startTime?: string;
        endTime?: string;
        isAllDay?: boolean;
        unavailableMembers?: UnavailabilityInfo[];
        [key: string]: string | boolean | undefined | UnavailabilityInfo[];
    };
}

const CalendarView: React.FC<CalendarViewProps> = ({
    events,
    isMobile = false,
    showAvailability = true,
    location = ''
}) => {
    // Helper function to extract member name from availability event
    const getMemberName = (summary: string): string => {
        if (!summary) return 'Unknown';
        const splitChars = [' ', '-', ':', '/'];
        const parts = summary.split(new RegExp(`[${splitChars.join('')}]`), 2);
        if (parts.length >= 1) {
            const memberName = parts[0].trim();
            if (memberName.length > 0) {
                return memberName.charAt(0).toUpperCase() + memberName.substring(1).toLowerCase();
            }
        }
        return 'Unknown';
    };

    const calendarEvents = useMemo(() => {
        const filteredEvents = events.filter(event => {
            // Apply location filter only to gig events, not availability events
            if (!event.isAvailabilityEvent && location &&
                (!event.location || !event.location.toLowerCase().includes(location.toLowerCase()))) {
                return false;
            }
            // Handle the showAvailability toggle - if false, exclude availability events
            if (!showAvailability && event.isAvailabilityEvent) {
                return false;
            }
            return true;
        });

        return filteredEvents.map(event => {
            let startDate: Date;
            let endDate: Date;
            
            if (event.isAllDay) {
                // For all-day events, use start of day for start and handle end date properly
                startDate = moment(event.startTime).startOf('day').toDate();
                
                // For all-day events that end at midnight (exclusive end), subtract 1 day
                const originalEnd = moment(event.endTime);
                if (originalEnd.hour() === 0 && originalEnd.minute() === 0) {
                    endDate = originalEnd.subtract(1, 'day').endOf('day').toDate();
                } else {
                    endDate = originalEnd.endOf('day').toDate();
                }
                
                // Ensure end is at least the same day as start for single-day events
                if (moment(endDate).isBefore(startDate)) {
                    endDate = moment(startDate).endOf('day').toDate();
                }
            } else {
                // For timed events, use the exact times
                startDate = moment(event.startTime).toDate();
                endDate = moment(event.endTime).toDate();
            }
            return {
                id: event.id,
                title: event.isAvailabilityEvent 
                    ? `${getMemberName(event.summary)} N/A`
                    : event.summary,
                start: startDate,
                end: endDate,
                allDay: event.isAllDay,
                resource: {
                    ...event,
                    isAvailabilityEvent: event.isAvailabilityEvent,
                    location: event.location,
                    htmlLink: event.htmlLink
                }
            };
        });
    }, [events, showAvailability, location]);

    // Custom event component
    const EventComponent = ({ event }: EventProps<CalendarEventType>) => {
        const isAvailability = event.resource.isAvailabilityEvent;

        return (
            <div
                className={`calendar-event ${isAvailability ? 'availability-event' : 'gig-event'}`}
                title={isAvailability
                    ? `${event.title} - Unavailable`
                    : `${event.title}${event.resource.location ? ` at ${event.resource.location}` : ''}`
                }
            >
                <div className="event-content">
                    {isAvailability && <span className="availability-icon">🚫</span>}
                    <span className="event-title">{event.title}</span>
                </div>
            </div>
        );
    };

    const handleSelectEvent = (event: CalendarEventType) => {
        if (event.resource.htmlLink) {
            window.open(event.resource.htmlLink, '_blank');
        }
    };

    const { components, views, style } = useMemo(
        () => ({
            components: {
                event: EventComponent
            },
            views: [ Views.MONTH, Views.WEEK, Views.DAY, Views.AGENDA ],
            style: { height: isMobile ? 1000 : 1000 }
        }),
        []
    )

    const [view, setView] = React.useState(Views.MONTH as string);
    const onView = useCallback((newView: string) => setView(newView), [setView])

    const [date, setDate] = React.useState(new Date());
    const onNavigate = useCallback((newDate: React.SetStateAction<Date>) => setDate(newDate), [setDate])

    return (
        <div>
            <Calendar
                date={date}
                onNavigate={onNavigate}
                localizer={localizer}
                events={calendarEvents}
                startAccessor="start"
                endAccessor="end"
                titleAccessor="title"
                allDayAccessor="allDay"
                onSelectEvent={handleSelectEvent}
                components={components}
                style={style}
                popup={true}
                popupOffset={30}
                view={view as View}
                views={views}
                onView={onView}
                step={60}
                showMultiDayTimes={true}
                formats={{
                    dayFormat: (date, culture, localizer) => localizer?.format(date, 'DD', culture) || '',
                    dayHeaderFormat: (date, culture, localizer) => localizer?.format(date, 'dddd', culture) || '',
                    monthHeaderFormat: (date, culture, localizer) => localizer?.format(date, 'MMMM YYYY', culture) || ''
                }}
                eventPropGetter={(event: CalendarEventType) => ({
                    className: event.resource.isAvailabilityEvent
                        ? 'availability-event-slot'
                        : 'gig-event-slot',
                    style: {
                        backgroundColor: event.resource.isAvailabilityEvent ? '#dc3545' : '#4a95b0',
                        borderColor: event.resource.isAvailabilityEvent ? '#c82333' : '#3a7a90',
                        color: 'white'
                    }
                })}
            />
        </div>
    );
};

export default CalendarView;
