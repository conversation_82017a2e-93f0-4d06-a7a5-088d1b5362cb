/* Compact Availability Card Styles */
.availability-item {
    display: inline-block;
    background: #ff5252;
    color: white;
    margin: 0.25rem 0.25rem 0.25rem 0;
    transition: all 0.2s ease;
    font-size: 0.8rem;
    padding: 2px 8px;
    border-radius: 12px;
    font-weight: bold;
}

.availability-item:hover {
    background: #c82333;
    transform: translateY(-1px);
    box-shadow: 0 2px 6px rgba(220, 53, 69, 0.4);
}

.availability-link {
    display: flex;
    align-items: center;
    gap: 0.4rem;
    text-decoration: none;
    color: white;
}

.availability-link:hover {
    color: white;
    text-decoration: none;
}

.availability-content {
    display: flex;
    align-items: center;
    gap: 0.4rem;
}

.availability-icon {
    font-size: 1rem;
    flex-shrink: 0;
}

.availability-member {
    white-space: nowrap;
    font-weight: 600;
}

/* Container for availability events to enable horizontal layout */
.availability-events-container {
    padding: 12px;
    margin-bottom: 10px;
    border-radius: 6px;
    background-color: var(--card-bg);
    box-shadow: 0 1px 3px var(--shadow-color);
    border-left: 4px solid var(--accent);
    flex-direction: row;
    align-items: flex-start;
    display: flex;
    flex-wrap: wrap;
    gap: 0;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .availability-item {
        background: #e74c3c;
        box-shadow: 0 1px 3px rgba(231, 76, 60, 0.3);
    }
    
    .availability-item:hover {
        background: #c0392b;
        box-shadow: 0 2px 6px rgba(231, 76, 60, 0.4);
    }
}

/* Mobile responsive */
@media (max-width: 768px) {
    .availability-item {
        font-size: 0.8rem;
        margin: 0.2rem 0.2rem 0.2rem 0;
    }
    
    .availability-icon {
        font-size: 0.9rem;
    }
}

/* Ensure availability events don't break the layout */
.availability-events-container .availability-item:last-child {
    margin-right: 0;
}
