import React from 'react';
import { CalendarEvent } from '../../services/BandCalendarApiService';
import './AvailabilityCard.css';

interface AvailabilityCardProps {
    event: CalendarEvent;
}

const AvailabilityCard: React.FC<AvailabilityCardProps> = ({ event }) => {
    // Extract member name from summary
    const getMemberName = (summary: string): string => {
        if (!summary) return 'Unknown';
        const splitChars = [' ', '-', ':', '/'];
        const parts = summary.split(new RegExp(`[${splitChars.join('')}]`), 2);
        if (parts.length >= 1) {
            const memberName = parts[0].trim();
            if (memberName.length > 0) {
                return memberName.charAt(0).toUpperCase() + memberName.substring(1).toLowerCase();
            }
        }
        return 'Unknown';
    };

    const memberName = getMemberName(event.summary);

    const cardContent = (
        <>
            <span className="availability-member">{memberName} N/A</span>
        </>
    );

    return (
        <div className="availability-item">
            {event.htmlLink ? (
                <a
                    href={event.htmlLink}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="availability-link"
                    title={`View ${memberName}'s availability in Google Calendar`}
                >
                    {cardContent}
                </a>
            ) : (
                <div className="availability-content">
                    {cardContent}
                </div>
            )}
        </div>
    );
};

export default AvailabilityCard;
