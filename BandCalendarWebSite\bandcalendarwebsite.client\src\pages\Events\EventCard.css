/* EventCard.css */
.gig-item {
    padding: 12px;
    margin-bottom: 10px;
    border-radius: 6px;
    background-color: var(--card-bg);
    box-shadow: 0 1px 3px var(--shadow-color);
    border-left: 4px solid var(--accent);
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.gig-item:first-child {
    border-top: none;
}

.gig-item:last-child {
    margin-bottom: 0;
}

.gig-item.past-event {
    opacity: 0.7;
    background-color: var(--past-event-bg);
    border-left-color: var(--neutral-light);
    font-style: italic;
}

.event-header {
    display: flex;
    align-items: center;
    width: 100%;
    margin-bottom: 8px;
}

.calendar-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    margin-right: 8px;
    color: var(--accent);
}

.gig-times {
    color: var(--text-dark);
    font-weight: 500;
    font-size: 0.9em;
    white-space: nowrap;
    display: inline-block;
}

.gig-summary {
    font-weight: bold;
    margin-bottom: 10px;
    display: block;
    line-height: 1.3;
}

.event-title-link {
    color: var(--text-dark);
    text-decoration: none;
    font-weight: bold;
    transition: color 0.2s ease;
    display: inline-block;
}

.event-title-link:hover {
    color: var(--accent);
    text-decoration: underline;
}

.gig-location {
    margin-top: 4px;
    font-size: 0.85em;
    display: flex;
    align-items: flex-start;
}

.location-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    margin-right: 6px;
    color: var(--accent);
    margin-top: 2px;
}

.gig-location a {
    color: var(--text-medium);
    text-decoration: none;
    font-weight: normal;
    flex: 1;
}

.gig-location a:hover {
    text-decoration: underline;
}

.gig-description {
    color: var(--text-medium);
    font-style: italic;
    margin-top: 4px;
    font-size: 0.9em;
}

/* Past event specific styling */
.gig-item.past-event .gig-times,
.gig-item.past-event .gig-summary,
.gig-item.past-event .gig-location a,
.gig-item.past-event .event-title-link,
.gig-item.past-event .gig-description {
    color: var(--past-event-text);
}

.gig-item.past-event .calendar-icon,
.gig-item.past-event .location-icon {
    color: var(--neutral-light);
}

/* Warning box styling */
.warning-box {
    background-color: var(--warning-bg);
    color: var(--warning-text);
    padding: 8px 12px;
    border-radius: 4px;
    margin: 8px 0;
    font-size: 0.85em;
    display: flex;
    align-items: center;
    border-left: 3px solid var(--warning);
}

.warning-icon {
    margin-right: 8px;
    font-size: 1.1em;
}
