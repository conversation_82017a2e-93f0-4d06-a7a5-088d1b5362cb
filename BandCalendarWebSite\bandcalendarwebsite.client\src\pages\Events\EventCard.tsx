﻿import React from 'react';
import WeatherIcon from './WeatherIcon.tsx';
import { UnavailabilityPills } from './UnavailabilityPills.tsx';
import { CalendarEvent } from '../../services/BandCalendarApiService.ts';
import { eventHasInvalidSummary, isEventInPast } from '../../utils/eventUtils.ts';
import './EventCard.css';

interface EventCardProps {
    event: CalendarEvent;
}

const formatEventTime = (startTime: string, endTime: string, isAllDay: boolean): React.ReactNode => {
    if (isAllDay) {
        return (
            <div className="warning-box">
                <span className="warning-icon" role="img" aria-label="warning">⚠️</span>
                Start and end times are missing
            </div>
        );
    }
    const start = new Date(startTime);
    const end = new Date(endTime);
    return `${start.toLocaleTimeString([], { hour: 'numeric', minute: '2-digit' })} - ${end.toLocaleTimeString([], { hour: 'numeric', minute: '2-digit' })}`;
};

const createGoogleMapsUrl = (location: string) => {
    return `https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(location)}`;
};

const EventCard: React.FC<EventCardProps> = ({ event }) => {
    const isPastEvent = isEventInPast(event);
    const timeDisplay = formatEventTime(event.startTime, event.endTime, event.isAllDay);

    return (
        <div className={`gig-item ${isPastEvent ? 'past-event' : ''}`}>
            <div className="event-header">
                <span className="calendar-icon" role="img" aria-label="calendar">📅</span>
                <span className="gig-times">{timeDisplay}</span>
            </div>
            <div className="gig-summary">
                {event.htmlLink ? (
                    <a
                        href={event.htmlLink}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="event-title-link"
                        title="View in Google Calendar"
                    >
                        {event.summary}
                    </a>
                ) : (
                    event.summary
                )}

                {event.location && !isPastEvent && (
                    <WeatherIcon
                        eventStartTime={event.startTime}
                        eventEndTime={event.endTime}
                        location={event.location}
                    />
                )}

                {eventHasInvalidSummary(event.summary) && (
                    <div
                        className="warning-box"
                        style={{ display: 'inline-block', marginLeft: '10px' }}
                    >
                        <span className="warning-icon" role="img" aria-label="warning">⚠️</span>
                        Unexpected gig name
                    </div>
                )}
            </div>
            {event.location ? (
                <div className="gig-location">
                    <span className="location-icon">📍</span>
                    <a
                        href={event.mapLink || createGoogleMapsUrl(event.location)}
                        target="_blank"
                        rel="noopener noreferrer"
                        title={event.location}
                    >
                        {event.location}
                    </a>
                </div>
            ) : (
                <div className="warning-box">
                        <span className="warning-icon" role="img" aria-label="warning">⚠️</span>
                    Location is missing
                </div>
            )}
            {event.description && (
                <div className="gig-description">{event.description}</div>
            )}

            {event.unavailableMembers && event.unavailableMembers.length > 0 && (
                <UnavailabilityPills unavailableMembers={event.unavailableMembers} />
            )}
        </div>
    );
};

export default EventCard;