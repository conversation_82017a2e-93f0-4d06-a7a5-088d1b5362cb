import React from 'react';

interface EventFiltersProps {
  year: string;
  location: string;
  years: string[];
  locations: string[];
  onYearChange: (year: string | React.ChangeEvent<HTMLSelectElement>) => void;
  onLocationChange: (location: string | React.ChangeEvent<HTMLSelectElement>) => void;
  onRefresh: () => void;
  showWarningsOnly?: boolean;
  onWarningsFilterChange?: (showWarningsOnly: boolean) => void;
  showPastEvents?: boolean;
  onPastEventsFilterChange?: (showPastEvents: boolean) => void;
  showAvailability?: 'off' | 'mine' | 'all';
  onAvailabilityFilterChange?: (showAvailability: 'off' | 'mine' | 'all') => void;
}

const EventFilters: React.FC<EventFiltersProps> = ({
  year,
  location,
  years,
  locations,
  onYearChange,
  onLocationChange,
  onRefresh,
  showWarningsOnly = false,
  onWarningsFilterChange = () => {},
  showPastEvents = true,
  onPastEventsFilterChange = () => {},
  showAvailability = 'mine',
  onAvailabilityFilterChange = () => {}
}) => {
  // No longer need collapse state for the filter menu

  const handleWarningsFilterChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onWarningsFilterChange(e.target.checked);
  };

  const handlePastEventsFilterChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onPastEventsFilterChange(e.target.checked);
  };

  const handleAvailabilityFilterChange = (value: 'off' | 'mine' | 'all') => {
    onAvailabilityFilterChange(value);
  };

  return (
    <div className="compact-filters">
      <div className="filter-content">
        <div className="filter-row">
          <div className="year-filter">
            <select
              value={year}
              onChange={(e) => onYearChange(e.target.value)}
              aria-label="Select Year"
            >
              {years.map(y => (
                <option key={y} value={y}>{y}</option>
              ))}
            </select>
          </div>

          <div className="location-filter">
            <select
              value={location}
              onChange={(e) => onLocationChange(e.target.value)}
              aria-label="Select Location"
              title={location}
            >
              <option value="">All Locations</option>
              {locations.map(loc => (
                <option key={loc} value={loc} title={loc}>{loc}</option>
              ))}
            </select>
          </div>
        </div>

        <div className="filter-row warnings-filter">
          <div className="warning-section" style={{ width: '100%' }}>
            <label className="pill-checkbox-label" htmlFor="warningsFilterMobile" style={{ width: '100%' }}>
              <input
                type="checkbox"
                id="warningsFilterMobile"
                checked={showWarningsOnly}
                onChange={handleWarningsFilterChange}
              />
              <span className="toggle-switch"></span>
              <span className="pill-icon">⚠️</span>
              <span className="pill-text">Show warnings only</span>
            </label>
          </div>
        </div>

        <div className="filter-row past-events-filter">
          <div className="past-events-section" style={{ width: '100%' }}>
            <label className="pill-checkbox-label" htmlFor="pastEventsFilterMobile" style={{ width: '100%' }}>
              <input
                type="checkbox"
                id="pastEventsFilterMobile"
                checked={showPastEvents}
                onChange={handlePastEventsFilterChange}
              />
              <span className="toggle-switch"></span>
              <span className="pill-icon">🕒</span>
              <span className="pill-text">Show past events</span>
            </label>
          </div>
        </div>

        <div className="filter-row availability-filter">
          <div className="availability-section" style={{ width: '100%' }}>
            <div className="availability-label">
              <span className="availability-icon">🚫</span>
              <span>Show availability:</span>
            </div>
            <div className="availability-toggle-group">
              <button
                type="button"
                className={`availability-toggle-button ${showAvailability === 'off' ? 'active' : ''}`}
                onClick={() => handleAvailabilityFilterChange('off')}
              >
                None
              </button>
              <button
                type="button"
                className={`availability-toggle-button ${showAvailability === 'mine' ? 'active' : ''}`}
                onClick={() => handleAvailabilityFilterChange('mine')}
              >
                Mine
              </button>
              <button
                type="button"
                className={`availability-toggle-button ${showAvailability === 'all' ? 'active' : ''}`}
                onClick={() => handleAvailabilityFilterChange('all')}
              >
                All
              </button>
            </div>
          </div>
        </div>

        <div className="filter-actions" style={{ borderBottom: 'none' }}>
          <button onClick={onRefresh}>
            <i className="bi bi-arrow-clockwise"></i>
            Refresh Data
          </button>
        </div>
      </div>
    </div>
  );
};

export default EventFilters;
