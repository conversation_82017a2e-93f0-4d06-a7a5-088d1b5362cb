import React from 'react';
import { format } from 'date-fns';
import { CalendarEvent as Event } from '../../services/BandCalendarApiService.ts';
import { isEventInPast } from '../../utils/eventUtils.ts';
import EventCard from './EventCard';
import AvailabilityCard from './AvailabilityCard';
import './EventsList.css';

interface EventsListProps {
    events: Event[];
    showEmptyWeekends: boolean;
}

const EventsList: React.FC<EventsListProps> = ({
    events,
    showEmptyWeekends
}) => {
    // This function ensures consistent date handling by setting the time to noon
    // to avoid any timezone offset issues when displaying dates
    const adjustForTimezone = (dateString: string) => {
        if (!dateString) return new Date();

        // For string dates (dateKey)
        if (typeof dateString === 'string' && dateString.includes('-')) {
            // If it's a yyyy-MM-dd format string
            if (dateString.length === 10 && dateString.match(/^\d{4}-\d{2}-\d{2}$/)) {
                const [year, month, day] = dateString.split('-').map(Number);
                // Set to noon to avoid timezone issues
                return new Date(year, month - 1, day, 12, 0, 0);
            }
        }

        // For ISO date strings from API or Date objects
        const date = new Date(dateString);

        // Check if valid date
        if (isNaN(date.getTime())) return new Date();

        // Create a date with same year, month, day but at noon to avoid timezone issues
        // This ensures the date is displayed correctly regardless of timezone
        return new Date(date.getFullYear(), date.getMonth(), date.getDate(), 12, 0, 0);
    };

    const groupEventsByMonth = (events: Event[]) => {
        return events.reduce((acc, event) => {
            const eventDate = adjustForTimezone(event.startTime);
            const yearMonth = `${eventDate.getFullYear()}-${eventDate.getMonth()}`;
            if (!acc[yearMonth]) {
                acc[yearMonth] = {
                    year: eventDate.getFullYear(),
                    month: eventDate.getMonth(),
                    events: []
                };
            }
            acc[yearMonth].events.push(event);
            return acc;
        }, {} as Record<string, { year: number; month: number; events: Event[] }>);
    };

    const groupEventsByDate = (eventsInMonth: Event[]) => {
        return eventsInMonth.reduce((acc, event) => {
            if (event.isAvailabilityEvent) {
                // For availability events, create entries for each day they span
                const startDate = new Date(event.startTime);
                const endDate = new Date(event.endTime);
                
                // For all-day events, the end date is exclusive, so subtract 1ms to get the actual last day
                let actualEndDate = endDate;
                if (event.isAllDay && endDate.getHours() === 0 && endDate.getMinutes() === 0) {
                    actualEndDate = new Date(endDate.getTime() - 1);
                }
                
                // Create entries for each day in the range
                const currentDate = new Date(startDate);
                while (currentDate <= actualEndDate) {
                    const dateKey = format(currentDate, 'yyyy-MM-dd');
                    if (!acc[dateKey]) {
                        acc[dateKey] = [];
                    }
                    acc[dateKey].push(event);
                    currentDate.setDate(currentDate.getDate() + 1);
                }
            } else {
                // For gig events, use the original logic (single day only)
                const eventDate = adjustForTimezone(event.startTime);
                const dateKey = format(eventDate, 'yyyy-MM-dd');
                if (!acc[dateKey]) {
                    acc[dateKey] = [];
                }
                acc[dateKey].push(event);
            }
            return acc;
        }, {} as Record<string, Event[]>);
    };

    // Helper function to get day of week (0 = Sunday, 1 = Monday, etc.)
    const getDayOfWeek = (date: Date): number => {
        return date.getDay();
    };

    // Helper function to check if a date is part of a weekend (Thursday-Monday)
    const isWeekendDay = (day: number): boolean => {
        // Thursday = 4, Friday = 5, Saturday = 6, Sunday = 0, Monday = 1
        return day === 4 || day === 5 || day === 6 || day === 0 || day === 1;
    };

    // Helper function to get the Friday of a given week
    const getFridayOfWeek = (date: Date): Date => {
        const day = date.getDay();
        const diff = day === 5 ? 0 : day === 6 ? -1 : day === 0 ? -2 : day === 1 ? -3 : 1; // Adjust to get to Friday
        const fridayDate = new Date(date);
        fridayDate.setDate(date.getDate() + diff);
        return fridayDate;
    };

    // Find weekend runs (Thursday-Monday) and add empty weekends for future dates
    const findWeekendRuns = (dates: string[], year: number, month: number) => {
        // Sort dates chronologically
        const sortedDates = [...dates].sort((a, b) => new Date(a).getTime() - new Date(b).getTime());

        // Group dates by weekend
        const weekendGroups: Record<string, string[]> = {};

        // Process each date with events
        sortedDates.forEach(date => {
            const dateObj = adjustForTimezone(date);
            const dayOfWeek = getDayOfWeek(dateObj);

            // If it's a weekend day (Thu-Mon), find its Friday
            if (isWeekendDay(dayOfWeek)) {
                const fridayDate = getFridayOfWeek(dateObj);
                const fridayKey = format(fridayDate, 'yyyy-MM-dd');

                if (!weekendGroups[fridayKey]) {
                    weekendGroups[fridayKey] = [];
                }

                if (!weekendGroups[fridayKey].includes(date)) {
                    weekendGroups[fridayKey].push(date);
                }
            } else {
                // For non-weekend days, create a separate group
                const dateKey = format(dateObj, 'yyyy-MM-dd');
                if (!weekendGroups[dateKey]) {
                    weekendGroups[dateKey] = [date];
                }
            }
        });

        // Only add empty weekends if no location filter is applied and not showing warnings only
        if (showEmptyWeekends) {
            // Add empty weekends for the month (only for future dates)
            const firstDayOfMonth = new Date(year, month, 1);
            const lastDayOfMonth = new Date(year, month + 1, 0);
            const today = new Date();
            today.setHours(0, 0, 0, 0); // Set to beginning of today for accurate comparison

            // Find all Fridays in the month
            const currentDate = new Date(firstDayOfMonth);
            while (currentDate <= lastDayOfMonth) {
                if (getDayOfWeek(currentDate) === 5) { // Friday
                    const fridayKey = format(currentDate, 'yyyy-MM-dd');

                    // Only add empty weekends for current or future dates
                    if (!weekendGroups[fridayKey] && currentDate >= today) {
                        weekendGroups[fridayKey] = [];
                    }
                }
                currentDate.setDate(currentDate.getDate() + 1);
            }
        }

        // Convert to array format and sort by date
        return Object.entries(weekendGroups)
            .sort(([a], [b]) => new Date(a).getTime() - new Date(b).getTime())
            .map(([fridayKey, dates]) => ({ fridayKey, dates }));
    };

    return (
        <div>
            {Object.values(groupEventsByMonth(events))
            .sort((a, b) => a.year - b.year || a.month - b.month)
            .map(monthGroup => {
                const monthDate = new Date(monthGroup.year, monthGroup.month, 1);
                const monthName = format(monthDate, 'MMMM yyyy');
                const eventsByDate = groupEventsByDate(monthGroup.events);
                const dateKeys = Object.keys(eventsByDate);
                const weekendRuns = findWeekendRuns(dateKeys, monthGroup.year, monthGroup.month);
                
                // Count only gig events (exclude availability events) for the monthly count
                const gigCount = monthGroup.events.filter(event => !event.isAvailabilityEvent).length;
                
                return (
                    <div key={`${monthGroup.year}-${monthGroup.month}`}>
                        <div className="month-header">
                            <h2>{monthName}</h2>
                            <span className="month-count">{gigCount} {gigCount === 1 ? 'gig' : 'gigs'}</span>
                        </div>

                        {weekendRuns
                            .map((weekend, runIndex) => {
                            return (
                                <div key={`weekend-${runIndex}`} className="run-container">
                                    {weekend.dates.length === 0 ? (
                                        // Empty weekend
                                        <div className="empty-weekend">
                                            <div className="gig-header">
                                                <span className="gig-date">Weekend of {format(adjustForTimezone(weekend.fridayKey), 'MMMM d, yyyy')}</span>
                                            </div>
                                            <div className="no-weekend-events">No events scheduled this weekend</div>
                                        </div>
                                    ) : (
                                    // Weekend with events
                                    weekend.dates.map(dateKey => {
                                        const eventsOnDate = eventsByDate[dateKey];
                                        const allEventsInPast = eventsOnDate.every(isEventInPast);

                                        return (
                                            <div key={dateKey} className="gig-day">
                                                <div className={`gig-header ${allEventsInPast ? 'past-date' : ''}`}>
                                                    <span className="gig-date">{format(adjustForTimezone(dateKey), 'EEEE, MMMM d, yyyy')}</span>
                                                </div>

                                                {(() => {
                                                    // Separate gig events from availability events
                                                    const gigEvents = eventsOnDate.filter(event => !event.isAvailabilityEvent);
                                                    const availabilityEvents = eventsOnDate.filter(event => event.isAvailabilityEvent);
                                                    
                                                    return (
                                                        <>
                                                            {/* Render gig events first */}
                                                            {gigEvents.map((event) => (
                                                                <EventCard
                                                                    key={event.id}
                                                                    event={event}
                                                                />
                                                            ))}

                                                            {/*
                                                            Render availability events if there are no gig events (as UnavailabilityPills pills
                                                            will be rendered on EventCard in that case).
                                                            TODO: Would be nice to unify the UnavailabilityPills component with AvailabilityCard so
                                                                    we have a single way of displaying these events (including links on EventCard).
                                                             */}
                                                            {gigEvents.length == 0 && availabilityEvents.length > 0 && (
                                                                <div className="availability-events-container">
                                                                    {availabilityEvents.map((event) => (
                                                                        <AvailabilityCard
                                                                            key={event.id}
                                                                            event={event}
                                                                        />
                                                                    ))}
                                                                </div>
                                                            )}
                                                        </>
                                                    );
                                                })()}
                                            </div>
                                        );
                                    })
                                )}
                                </div>
                        );
                        })}
                    </div>
                );
            })}
        </div>
    );
};

export default EventsList;
