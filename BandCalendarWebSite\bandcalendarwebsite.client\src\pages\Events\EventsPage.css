/* EventsPage.css */
.event-container {
    width: 100%;
    margin-bottom: 20px;
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 10px;
    line-height: 1.6;
    background-color: var(--bg-main);
}

.events-content {
    min-height: 300px;
    transition: opacity 0.2s ease-in-out;
    position: relative;
}

.events-content.loading {
    opacity: 0.6;
}

.loading-indicator {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    padding: 10px 20px;
    background: rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    z-index: 10;
}

/* Error message */
.error {
    color: var(--danger);
    text-align: center;
    margin-bottom: 20px;
}

/* No gigs message */
.no-gigs {
    color: var(--text-medium);
    font-style: italic;
    text-align: center;
    padding: 40px 0;
}

/* Unavailability pills */
.unavailability-pills {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    margin-top: 5px;
}

.unavailability-pill {
    display: inline-block;
    background-color: #ff5252;
    color: white;
    font-size: 0.8rem;
    padding: 2px 8px;
    border-radius: 12px;
    font-weight: bold;
}

/* Mobile-specific optimizations */
@media (max-width: 768px) {
    .gig-item {
        margin-bottom: 12px;
        border-radius: 8px;
    }

    .event-header {
        display: flex;
        align-items: center;
        margin-bottom: 4px;
    }

    .calendar-icon {
        margin-right: 8px;
    }

    .gig-times {
        font-weight: 500;
    }

    .gig-summary {
        margin-bottom: 4px;
        font-weight: 500;
    }

    .gig-location {
        display: flex;
        align-items: flex-start;
    }

    .location-icon {
        margin-right: 8px;
        flex-shrink: 0;
        margin-top: 2px;
    }
}