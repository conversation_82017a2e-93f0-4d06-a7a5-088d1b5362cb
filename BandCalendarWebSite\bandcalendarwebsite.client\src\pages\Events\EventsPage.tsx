import React, { useEffect, useState } from 'react';
import { useSearchParams } from 'react-router-dom';
import EventFilters from './EventFilters.tsx';
import PullToRefreshWrapper from '../../components/PullToRefreshWrapper.tsx';
import BandCalendarApiService, {CalendarEvent as Event} from '../../services/BandCalendarApiService.ts';
import { isEventInPast, eventHasWarnings } from '../../utils/eventUtils.ts';
import { useFilterRenderer } from '../../hooks';
import './EventsPage.css';
import EventsList from './EventsList';

interface EventsPageProps {
    isMobile?: boolean;
}

const EventsPage: React.FC<EventsPageProps> = ({ isMobile = false }) => {
    const [searchParams, setSearchParams] = useSearchParams();
    const [allEvents, setAllEvents] = useState<Event[]>([]);
    const [filteredEvents, setFilteredEvents] = useState<Event[]>([]);
    const [selectedYear, setSelectedYear] = useState<number>(new Date().getFullYear());
    const [location, setLocation] = useState(searchParams.get('location') || '');
    const [locations, setLocations] = useState<string[]>([]);
    const [showWarningsOnly, setShowWarningsOnly] = useState(searchParams.get('warnings') === 'true');
    const [showPastEvents, setShowPastEvents] = useState(searchParams.get('past') === 'true');
    const [showAvailability, setShowAvailability] = useState<'off' | 'mine' | 'all'>(
        (searchParams.get('availability') as 'off' | 'mine' | 'all') || 'off'
    );
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    // Generate an array of years for the dropdown (current year and 5 years back)
    const years = [2025, 2026];
    const yearStrings = years.map(y => y.toString());

    // Effect to handle URL query parameters
    useEffect(() => {
        const locationParam = searchParams.get('location');
        const yearParam = searchParams.get('year');
        const warningsParam = searchParams.get('warnings');
        const pastParam = searchParams.get('past');
        const availabilityParam = searchParams.get('availability');

        // Update location state if URL parameter changes
        if (locationParam !== location) {
            setLocation(locationParam || '');
        }

        // Update year and fetch events if year parameter changes
        if (yearParam && !isNaN(parseInt(yearParam)) && parseInt(yearParam) !== selectedYear) {
            setSelectedYear(parseInt(yearParam));
        }

        // Update warnings filter if URL parameter changes
        const newWarningsValue = warningsParam === 'true';
        if (newWarningsValue !== showWarningsOnly) {
            setShowWarningsOnly(newWarningsValue);
        }

        // Update past events filter if URL parameter changes
        const newPastEventsValue = pastParam === 'true';
        if (newPastEventsValue !== showPastEvents) {
            setShowPastEvents(newPastEventsValue);
        }

        // Update availability filter if URL parameter changes
        const newAvailabilityValue = (availabilityParam as 'off' | 'mine' | 'all') || 'off';
        if (newAvailabilityValue !== showAvailability) {
            setShowAvailability(newAvailabilityValue);
        }
    }, [searchParams, location, selectedYear, showWarningsOnly, showPastEvents, showAvailability]);

    // Effect to fetch events when year changes
    useEffect(() => {
        fetchEvents();
    }, [selectedYear]);

    // Effect to filter events when location, warnings filter, past events filter, availability filter, or all events change
    useEffect(() => {
        filterEvents();
    }, [location, showWarningsOnly, showPastEvents, showAvailability, allEvents]);

    // Function to filter events by location, warnings, past events, and availability
    const filterEvents = () => {
        const shouldShowAvailabilityEvent = (event: Event): boolean => {
            if (showAvailability === 'off' || isEventInPast(event) || location) {
                return false; // Hide all availability events
            }
            else if (showAvailability === 'mine') {
                // Only show Dave's availability events
                const memberName = getMemberNameFromEvent(event);
                return memberName.toLowerCase() === 'dave';
            }
            // For 'all', show all availability events
            return true;
        }

        setFilteredEvents(allEvents.filter(event => {
            if (showWarningsOnly && !eventHasWarnings(event)) {
                return false;
            }

            if (location && (!event.location || !event.location.toLowerCase().includes(location.toLowerCase()))) {
                return false;
            }

            if (!showPastEvents && isEventInPast(event)) {
                return false;
            }

            if (event.isAvailabilityEvent) {
                return shouldShowAvailabilityEvent(event);
            }

            return true;
        }));
    };

    // Helper function to extract member name from availability event
    const getMemberNameFromEvent = (event: Event): string => {
        if (!event.summary) return '';
        const splitChars = [' ', '-', ':', '/'];
        const parts = event.summary.split(new RegExp(`[${splitChars.join('')}]`), 2);
        if (parts.length >= 1) {
            const memberName = parts[0].trim();
            if (memberName.length > 0) {
                return memberName.charAt(0).toUpperCase() + memberName.substring(1).toLowerCase();
            }
        }
        return '';
    };

    const fetchEvents = async () => {
        setLoading(true);
        setError(null);

        try {
            // Set date range for the selected year (Jan 1 to Dec 31)
            const startDate = `${selectedYear}-01-01`;
            const endDate = `${selectedYear}-12-31`;

            // Fetch all events for the year without location filter
            const data = await BandCalendarApiService.getGigEvents(startDate, endDate, null);
            setAllEvents(data);

            // Extract unique locations from gig events data (exclude availability events)
            const uniqueLocations = data
                .filter(event => !event.isAvailabilityEvent && event.location && event.location.trim() !== '')
                .map(event => event.location)
                .filter((loc, index, self) => self.indexOf(loc) === index)
                .sort();

            setLocations(uniqueLocations);

            // Filtered events will be set by the useEffect that depends on location and allEvents
        } catch (err) {
            console.error('Error fetching events:', err);
            setError('Failed to load events. Please try again later.');
            setAllEvents([]);
            setFilteredEvents([]);
        } finally {
            setLoading(false);
        }
    };

    // Handle location change for both mobile and desktop views
    const handleLocationChange = (locationValue: string | React.ChangeEvent<HTMLSelectElement>) => {
        // Handle both direct string value (mobile) and event (desktop)
        const newLocation = typeof locationValue === 'string'
            ? locationValue
            : locationValue.target.value;

        // Update URL query parameters
        updateSearchParams(newLocation, selectedYear, showWarningsOnly, showPastEvents, showAvailability);
    };

    // Handle year change for both mobile and desktop views
    const handleYearChange = (yearValue: string | React.ChangeEvent<HTMLSelectElement>) => {
        // Handle both direct string value (mobile) and event (desktop)
        const yearString = typeof yearValue === 'string'
            ? yearValue
            : yearValue.target.value;

        const newYear = parseInt(yearString);
        updateSearchParams(location, newYear, showWarningsOnly, showPastEvents, showAvailability);
    };

    // Handle warnings filter change
    const handleWarningsFilterChange = (showWarnings: boolean) => {
        // Update URL query parameters
        updateSearchParams(location, selectedYear, showWarnings, showPastEvents, showAvailability);
    };

    // Handle past events filter change
    const handlePastEventsFilterChange = (showPast: boolean) => {
        // Update URL query parameters
        updateSearchParams(location, selectedYear, showWarningsOnly, showPast, showAvailability);
    };

    // Handle availability filter change
    const handleAvailabilityFilterChange = (availabilityMode: 'off' | 'mine' | 'all') => {
        // Update URL query parameters
        updateSearchParams(location, selectedYear, showWarningsOnly, showPastEvents, availabilityMode);
    };

    const updateSearchParams = (
        loc: string,
        year: number,
        warnings: boolean = showWarningsOnly,
        past: boolean = showPastEvents,
        availability: 'off' | 'mine' | 'all' = showAvailability
    ) => {
        const params = new URLSearchParams();

        if (loc) {
            params.set('location', loc);
        }

        params.set('year', year.toString());

        if (warnings) {
            params.set('warnings', 'true');
        }

        if (past) {
            params.set('past', 'true');
        }

        if (availability !== 'off') {
            params.set('availability', availability);
        }

        setSearchParams(params);
    };

    useFilterRenderer(
        () => (
            <EventFilters
                year={selectedYear.toString()}
                location={location}
                years={yearStrings}
                locations={locations}
                onYearChange={handleYearChange}
                onLocationChange={handleLocationChange}
                onRefresh={fetchEvents}
                showWarningsOnly={showWarningsOnly}
                onWarningsFilterChange={handleWarningsFilterChange}
                showPastEvents={showPastEvents}
                onPastEventsFilterChange={handlePastEventsFilterChange}
                showAvailability={showAvailability}
                onAvailabilityFilterChange={handleAvailabilityFilterChange}
            />
        ),
        [isMobile, selectedYear, location, locations, showWarningsOnly, showPastEvents, showAvailability]
    );

    if (filteredEvents.length === 0) {
        return (
            <p className="no-gigs">
                {location ?
                    `No gigs found at "${location}" for ${selectedYear}.` :
                    `No gigs scheduled for ${selectedYear}.`
                }
            </p>
        );
    }

    return (
        <div className="event-container">
            <PullToRefreshWrapper onRefresh={fetchEvents} isPullable={false}> 
                <div className={`events-content ${loading ? 'loading' : ''}`}>
                    {loading && <div className="loading-indicator">Loading...</div>}
                    {error && <p className="error">{error}</p>}

                {!loading && !error && (
                    <EventsList 
                        events={filteredEvents}
                        showEmptyWeekends={!location && !showWarningsOnly}
                    />
                )}
                </div>
            </PullToRefreshWrapper>
        </div>
    );
};

export default EventsPage;
