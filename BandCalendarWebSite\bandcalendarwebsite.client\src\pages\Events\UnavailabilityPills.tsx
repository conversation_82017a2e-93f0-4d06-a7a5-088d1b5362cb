// New component for unavailability pills
import React from "react";
import {UnavailabilityInfo} from "../../services/BandCalendarApiService.ts";

export const UnavailabilityPills: React.FC<{ unavailableMembers?: UnavailabilityInfo[] }> = ({unavailableMembers}) => {
    if (!unavailableMembers || unavailableMembers.length === 0) {
        return null;
    }

    return (
        <div className="unavailability-pills">
            {unavailableMembers.map((member, index) => (
                <span key={index} className="unavailability-pill">
                    {member.memberName} N/A
                </span>
            ))}
        </div>
    );
};