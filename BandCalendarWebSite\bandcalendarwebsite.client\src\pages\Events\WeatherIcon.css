.weather-icon-container {
    display: inline-flex;
    align-items: center;
    margin-left: 8px;
    position: relative;
    z-index: 101;
}

/* Z-index management for tooltips */
body.tooltip-active .weather-icon-container:not(.active) {
    z-index: 50 !important;
}

body.tooltip-active .weather-icon-container.active {
    z-index: 201 !important;
}

.weather-icon {
    font-size: 1.4rem;
    cursor: pointer;
}

.weather-tooltip-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 199;
    background-color: transparent;
}

.weather-tooltip {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 200;
    visibility: visible;
    opacity: 1;
    pointer-events: auto;
    max-width: 90%;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    background-color: var(--primary-darker);
}

/* Weather table styles */
.weather-table {
    width: auto;
    border-collapse: collapse;
    font-size: 1rem;
    color: white;
    overflow: hidden;
    border-spacing: 0;
    box-shadow: 0 2px 8px var(--shadow-color);
    border-radius: 6px;
}

.weather-table th,
.weather-table td {
    padding: 12px 16px;
    text-align: center;
    font-size: 0.9rem;
    white-space: nowrap;
}

.weather-table th:first-child,
.weather-table td:first-child {
    max-width: 80px;
    text-align: left;
}

.weather-table th {
    font-weight: bold;
    border-bottom: 1px solid rgba(255, 255, 255, 0.4);
    background-color: var(--primary-bg);
    color: var(--text-on-primary);
}

.weather-table td {
    border-bottom: 1px solid var(--neutral-lighter);
    background-color: var(--card-bg);
    color: var(--text-dark);
}

.loading-hourly {
    font-size: 1rem;
    text-align: center;
    padding: 15px;
    font-style: italic;
    color: white;
}

/* Mobile optimizations */
@media (max-width: 768px) {
    .weather-icon {
        font-size: 1.2rem;
    }

    .weather-tooltip {
        max-width: 95%;
        z-index: 999 !important;
    }

    .weather-tooltip-overlay {
        z-index: 998 !important;
    }

    body.tooltip-active .weather-icon-container.active {
        z-index: 1000 !important;
    }

    .weather-table {
        font-size: 0.9rem;
    }

    .weather-table th,
    .weather-table td {
        padding: 6px 8px;
    }

    .weather-table th:first-child,
    .weather-table td:first-child {
        max-width: 70px;
    }
}
