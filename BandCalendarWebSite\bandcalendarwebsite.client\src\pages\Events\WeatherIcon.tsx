import React, { useState, useEffect, useRef, useCallback } from 'react';
import WeatherService, { WeatherForecast } from '../../services/WeatherService.ts';
import geocodingService from '../../services/GeocodingService.ts';
import './WeatherIcon.css';
import {format} from "date-fns";

// Global state to track active tooltip
let activeTooltipId: string | null = null;
let activeTooltipSetter: ((show: boolean) => void) | null = null;

interface WeatherIconProps {
    eventStartTime: string;
    eventEndTime: string;
    location: string;
}

interface HourlyForecast {
    time: string;
    temperature: number;
    precipitation: number;
    precipitationProbability: number;
    windSpeed: number;
    weatherIcon: string;
    weatherDescription: string;
}

const WeatherIcon: React.FC<WeatherIconProps> = ({
    eventStartTime,
    eventEndTime,
    location
}) => {
    // Generate a unique ID for this tooltip instance
    const tooltipId = useRef(`tooltip-${Math.random().toString(36).substring(2, 11)}`).current;
    const [showTooltip, setShowTooltip] = useState(false);
    const [hourlyForecasts, setHourlyForecasts] = useState<HourlyForecast[]>([]);
    const [isLoadingHourly, setIsLoadingHourly] = useState(false);
    const [forecast, setForecast] = useState<WeatherForecast | null>(null);
    const [isLoading, setIsLoading] = useState(true);
    const tooltipRef = useRef<HTMLDivElement>(null);
    const isMobile = window.innerWidth <= 768;

    // Reference to the container element
    const containerRef = useRef<HTMLDivElement>(null);

    // Function to handle showing this tooltip
    const showThisTooltip = useCallback(() => {
        // If there's already an active tooltip, hide it first
        if (activeTooltipId && activeTooltipId !== tooltipId && activeTooltipSetter) {
            activeTooltipSetter(false);
        }

        // Set this as the active tooltip
        activeTooltipId = tooltipId;
        activeTooltipSetter = setShowTooltip;

        // Add tooltip-active class to body
        document.body.classList.add('tooltip-active');

        // First, reset all containers to default
        document.querySelectorAll('.weather-icon-container').forEach(container => {
            container.classList.remove('active');
            (container as HTMLElement).style.removeProperty('z-index');
        });

        // Add active class to this container
        if (containerRef.current) {
            containerRef.current.classList.add('active');
            containerRef.current.style.zIndex = isMobile ? '1000' : '201';
        }

        // Show this tooltip
        setShowTooltip(true);
    }, [isMobile, tooltipId]);

    // Function to hide this tooltip
    const hideThisTooltip = useCallback(() => {
        // Remove tooltip-active class from body
        document.body.classList.remove('tooltip-active');

        // Remove active class from this container
        if (containerRef.current) {
            containerRef.current.classList.remove('active');
        }

        // Reset z-index for all weather icon containers to their default
        document.querySelectorAll('.weather-icon-container').forEach(container => {
            // Remove any inline z-index style that might have been set
            (container as HTMLElement).style.removeProperty('z-index');
        });

        // Clear global tooltip state
        if (activeTooltipId === tooltipId) {
            activeTooltipId = null;
            activeTooltipSetter = null;
        }

        // Hide this tooltip
        setShowTooltip(false);
    }, [tooltipId]);

    // Close tooltip when clicking/tapping anywhere on the document
    useEffect(() => {
        // Create a transparent overlay that covers the entire screen when tooltip is visible
        const createOverlay = () => {
            const overlay = document.createElement('div');
            overlay.id = 'weather-tooltip-overlay';
            overlay.className = 'weather-tooltip-overlay';

            // When the overlay is clicked/tapped, close the tooltip
            overlay.addEventListener('click', (event) => {
                event.preventDefault();
                event.stopPropagation();
                hideThisTooltip();
                document.body.removeChild(overlay);
            });

            overlay.addEventListener('touchstart', (event) => {
                event.preventDefault();
                event.stopPropagation();
                hideThisTooltip();
                document.body.removeChild(overlay);
            }, { passive: false });

            document.body.appendChild(overlay);
            return overlay;
        };

        let overlay: HTMLElement | null = null;

        if (showTooltip) {
            // Create overlay when tooltip is shown
            overlay = createOverlay();

            // Ensure all tooltips have a lower z-index than this one
            document.querySelectorAll('.weather-icon-container').forEach(container => {
                if (container !== containerRef.current) {
                    (container as HTMLElement).style.zIndex = '50';
                }
            });
        }

        return () => {
            // Remove overlay when component unmounts or tooltip is hidden
            if (overlay && document.body.contains(overlay)) {
                document.body.removeChild(overlay);
            }

            // Clean up if this was the active tooltip
            if (activeTooltipId === tooltipId) {
                // Remove tooltip-active class from body
                document.body.classList.remove('tooltip-active');

                // Reset z-index for all weather icon containers
                document.querySelectorAll('.weather-icon-container').forEach(container => {
                    // Remove any inline z-index style that might have been set
                    (container as HTMLElement).style.removeProperty('z-index');
                    // Also ensure the 'active' class is removed
                    container.classList.remove('active');
                });

                // Clear global tooltip state
                activeTooltipId = null;
                activeTooltipSetter = null;
            }
        };
    }, [showTooltip, tooltipId]);

    // Scroll tooltip into view when shown
    useEffect(() => {
        if (showTooltip && tooltipRef.current) {
            // Use a small timeout to ensure the tooltip is rendered
            setTimeout(() => {
                tooltipRef.current?.scrollIntoView({
                    behavior: 'smooth',
                    block: 'nearest'
                });
            }, 100);
        }
    }, [showTooltip]);

    // Fetch hourly forecasts when tooltip is shown
    useEffect(() => {
        if (showTooltip && forecast && location && eventStartTime && eventEndTime) {
            fetchHourlyForecasts();
        }
    }, [showTooltip, forecast, location, eventStartTime, eventEndTime]);

    // Fetch weather data for this event location and time
    const fetchWeatherData = useCallback(async () => {
        // Skip if no location
        if (!location) {
            setIsLoading(false);
            return;
        }

        try {
            setIsLoading(true);
            // Fetch weather forecast for the event
            const eventForecast = await WeatherService.getEventWeather(
                location,
                eventStartTime,
                eventEndTime
            );
            setForecast(eventForecast);
        } catch (error) {
            console.error(`Error fetching weather:`, error);
            setForecast(null);
        } finally {
            setIsLoading(false);
        }
    }, [location, eventStartTime, eventEndTime]);

    const fetchHourlyForecasts = async () => {
        if (!location || !eventStartTime || !eventEndTime || isLoadingHourly) return;

        setIsLoadingHourly(true);

        try {
            // IMPORTANT: The API returns times in the timezone we specify (America/New_York)
            // and our event times are also in Eastern time, so we need to be careful with timezone handling

            // Parse event times - these are already in Eastern time
            const eventStart = new Date(eventStartTime);
            const eventEnd = new Date(eventEndTime);

            // Calculate 1 hour before event start and 1 hour after event end
            const extendedStart = new Date(eventStart);
            extendedStart.setHours(eventStart.getHours() - 1);

            const extendedEnd = new Date(eventEnd);
            extendedEnd.setHours(eventEnd.getHours() + 1);

            // Format dates for API - ensure we include both days for events spanning midnight
            const startDate = format(extendedStart, 'yyyy-MM-dd');
            const endDate = format(extendedEnd, 'yyyy-MM-dd');

            // Event spans multiple days if the start and end dates are different
            const isMultiDay = startDate !== endDate;

            // Get coordinates for the location
            const coordinates = await geocodingService.geocodeAddress(location);
            if (!coordinates) {
                console.warn(`Could not find coordinates for location: ${location}`);
                return;
            }

            // Get weather forecast for the date range
            const forecasts = await WeatherService.getWeatherForecast(
                coordinates.lat,
                coordinates.lng,
                startDate,
                endDate
            );

            // Process the forecasts to display hourly weather data

            // Filter forecasts to only include those during the extended event time
            // IMPORTANT: The forecast times from the API are in the format "2023-05-20T14:00" (Eastern time)
            // We need to parse these correctly to compare with our event times

            // Create extended time strings in the same format as the API returns
            const extendedStartHour = extendedStart.getHours().toString().padStart(2, '0');
            const extendedStartMinute = extendedStart.getMinutes().toString().padStart(2, '0');
            const extendedStartString = `${startDate}T${extendedStartHour}:${extendedStartMinute}`;

            const extendedEndHour = extendedEnd.getHours().toString().padStart(2, '0');
            const extendedEndMinute = extendedEnd.getMinutes().toString().padStart(2, '0');
            const extendedEndString = `${endDate}T${extendedEndHour}:${extendedEndMinute}`;

            // Create time strings for filtering in the correct format

            // Sort forecasts by time to ensure chronological order
            const filteredForecasts = forecasts
                .filter(forecast => {
                    // For multi-day events, we need special handling
                    if (isMultiDay) {
                        const forecastDate = forecast.time.split('T')[0];
                        const forecastHour = forecast.time.split('T')[1];

                        // If it's the start date, include hours from extendedStart onwards
                        if (forecastDate === startDate) {
                            return forecastHour >= extendedStartString.split('T')[1];
                        }

                        // If it's the end date, include hours up to extendedEnd
                        if (forecastDate === endDate) {
                            return forecastHour <= extendedEndString.split('T')[1];
                        }

                        // If it's a date in between, include all hours
                        return forecastDate > startDate && forecastDate < endDate;
                    } else {
                        // For same-day events, simple string comparison works
                        return forecast.time >= extendedStartString && forecast.time <= extendedEndString;
                    }
                })
                .sort((a, b) => {
                    // Sort by time string (which is already in ISO format)
                    return a.time.localeCompare(b.time);
                });

            setHourlyForecasts(filteredForecasts);
        } catch (error) {
            console.error('Error fetching hourly forecasts:', error);
        } finally {
            setIsLoadingHourly(false);
        }
    };

    const formatDate = (dateString: string) => {
        // The dateString is in format "2023-05-20T14:00" (Eastern time from API)
        // Parse it directly without timezone conversion
        const dateParts = dateString.split('T');
        const timeParts = dateParts[1].split(':');

        // Create a date object (browser will interpret as local time)
        const date = new Date(
            parseInt(dateParts[0].split('-')[0]), // year
            parseInt(dateParts[0].split('-')[1]) - 1, // month (0-based)
            parseInt(dateParts[0].split('-')[2]), // day
            parseInt(timeParts[0]), // hour
            parseInt(timeParts[1]) // minute
        );

        return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    };

    // Ref to store the timeout ID for delayed tooltip hiding
    const hideTimeoutRef = useRef<number | null>(null);

    // Clean up timeout on unmount
    useEffect(() => {
        return () => {
            if (hideTimeoutRef.current !== null) {
                window.clearTimeout(hideTimeoutRef.current);
            }
        };
    }, []);

    // Close tooltip when ESC key is pressed
    useEffect(() => {
        const handleEscKey = (event: KeyboardEvent) => {
            if (event.key === "Escape" && showTooltip) {
                hideThisTooltip();
            }
        };

        // Add event listener
        document.addEventListener('keydown', handleEscKey);

        // Clean up
        return () => {
            document.removeEventListener('keydown', handleEscKey);
        };
    }, [showTooltip, hideThisTooltip]);

    // Fetch the weather data when component mounts or when its props change
    useEffect(() => {
        if (location && eventStartTime && eventEndTime) {
            fetchWeatherData();
        }
    }, [location, eventStartTime, eventEndTime, fetchWeatherData]);
    

    if (!forecast) {
        return null;
    }

    return (
        <div
            ref={containerRef}
            className="weather-icon-container"
            onClick={(e) => {
                // Stop propagation to prevent parent elements from receiving the click
                e.stopPropagation();

                // Toggle tooltip with our global management function
                if (showTooltip) {
                    hideThisTooltip();
                } else {
                    showThisTooltip();
                }
            }}
        >
            <span 
                className="weather-icon" 
                title={forecast.weatherDescription}
                >{isLoading ? '⏳' : forecast.weatherIcon}</span>
            {showTooltip && (
                <div
                    ref={tooltipRef}
                    className="weather-tooltip"
                    onClick={(e) => {
                        // Prevent clicks on the tooltip from closing it
                        e.stopPropagation();
                    }}
                    style={{ touchAction: 'none' }} // Prevent default touch actions on mobile
                >
                    {hourlyForecasts.length > 0 ? (
                        <table className="weather-table">
                            <thead>
                                <tr>
                                    <th>Time</th>
                                    <th>Icon</th>
                                    <th>Temp</th>
                                    <th colSpan={2}>Precip</th>
                                    <th>Wind</th>
                                </tr>
                            </thead>
                            <tbody>
                                {hourlyForecasts.map((hourly, index) => (
                                    <tr key={index}>
                                        <td>{formatDate(hourly.time)}</td>
                                        <td>{hourly.weatherIcon}</td>
                                        <td>{hourly.temperature.toFixed(1)}°F</td>
                                        <td>{hourly.precipitationProbability}%</td>
                                        <td>{hourly.precipitation > 0 ? `${hourly.precipitation.toFixed(1)}"` : '0"'}</td>
                                        <td>{hourly.windSpeed.toFixed(1)}mph</td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    ) : isLoadingHourly ? (
                        <div className="loading-hourly">Loading hourly forecast...</div>
                    ) : (
                        <div className="loading-hourly">Failed to retrieve weather forecast</div>
                    )}
                </div>
            )}
        </div>
    );
};

export default WeatherIcon;
