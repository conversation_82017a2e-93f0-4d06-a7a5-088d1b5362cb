import React from 'react';
import './GoogleButton.css';

interface GoogleButtonProps {
  onClick: () => void;
}

const GoogleButton: React.FC<GoogleButtonProps> = ({ onClick }) => {
  return (
    <button type="button" className="google-btn" onClick={onClick}>
      <div className="google-icon-wrapper">
        <img 
          src="/google-logo.svg" 
          alt="Google logo" 
          className="google-icon" 
        />
      </div>
      <span className="btn-text">Sign in with Google</span>
    </button>
  );
};

export default GoogleButton;
