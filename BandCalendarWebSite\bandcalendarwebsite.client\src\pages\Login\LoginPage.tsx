import React, { useEffect, useState } from 'react';
import { useNavigate, useLocation, useSearchParams } from 'react-router-dom';
import axios from 'axios';
import GoogleButton from './GoogleButton.tsx';

const LoginPage: React.FC = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const navigate = useNavigate();
  const location = useLocation();
  const [searchParams] = useSearchParams();

  // Get the return URL from the location state or query parameter or default to '/'
  const returnUrlParam = searchParams.get('returnUrl');
  const from = returnUrlParam || location.state?.from?.pathname || '/';

  // Add login-page class to body and disable scrolling
  useEffect(() => {
    // Add login-page class to body
    document.body.classList.add('login-page');

    // Disable scrolling
    const originalStyle = window.getComputedStyle(document.body).overflow;
    document.body.style.overflow = 'hidden';

    // Cleanup function to remove class and restore scrolling when component unmounts
    return () => {
      document.body.classList.remove('login-page');
      document.body.style.overflow = originalStyle;
    };
  }, []);

  useEffect(() => {
    // Check if there's an error in the URL
    const errorParam = searchParams.get('error');
    if (errorParam) {
      // Map error codes to user-friendly messages
      const errorMessages: Record<string, string> = {
        'authentication_failed': 'Authentication failed. Please try again.',
        'access_denied': 'Access was denied by Google.',
        'unexpected_error': 'An unexpected error occurred. Please try again.',
      };

      setError(errorMessages[errorParam] || `Error: ${errorParam}`);
    }

    // Check if the user is already authenticated
    const checkAuthStatus = async () => {
      try {
        setIsLoading(true);
        const response = await axios.get('/api/auth/status');

        if (response.data.isAuthenticated) {
          // User is already authenticated, redirect to the original page
          navigate(from, { replace: true });
        } else {
          // User is not authenticated, show the login button
          setIsLoading(false);
        }
      } catch (err) {
        setError('Failed to check authentication status');
        setIsLoading(false);
      }
    };

    // Call the function to check authentication status
    checkAuthStatus();

    return () => {
    };
  }, [from, navigate, searchParams]);

  const handleLogin = () => {
    // Redirect to the Google login endpoint
    // Include the current URL as the return URL
    const returnUrl = from === '/' ? '' : from;

    // Log the login attempt
    console.log(`Initiating login with returnUrl: ${returnUrl}`);

    // Redirect to the login endpoint
    window.location.href = `/api/auth/login?returnUrl=${encodeURIComponent(returnUrl)}`;
  };
  return (
      <div className="d-flex justify-content-center align-items-center min-vh-100 w-100" style={{ backgroundColor: 'var(--primary-bg)', margin: 0, padding: 0 }}>
        { isLoading ? (
          <div className="text-center">
            <div className="spinner-border text-white" role="status">
              <span className="visually-hidden">Loading...</span>
            </div>
            <p className="mt-2 text-white">Checking authentication status...</p>
          </div>)
            : (
          <div className="card shadow-sm" style={{ maxWidth: '400px', width: '90%', backgroundColor: 'var(--card-bg)', color: 'var(--text-dark)' }}>
            <div className="card-body p-4">
              <h1 className="text-center mb-4">Polish Nannies Schedule</h1>
              {error && (
                <div className="alert alert-danger" role="alert">
                  <p className="mb-0">{error}</p>
                </div>
              )}
              <div className="text-center">
              <p className="mb-4">Please log in to access the band calendar.</p>
              <GoogleButton onClick={handleLogin} />
            </div>
        </div>
      </div> )}
    </div>
  );
};

export default LoginPage;
