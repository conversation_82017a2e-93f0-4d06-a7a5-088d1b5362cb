/* Venue Map Styles */
.venue-map-container {
    padding: 0;
    margin: 0;
    height: calc(100vh - 48px); /* Adjust for navbar height */
    width: 100%;
    display: flex;
    flex-direction: column;
    position: fixed; /* Changed from absolute to fixed */
    top: 48px; /* Position right below navbar */
    left: 0;
    right: 0;
    bottom: 0;
    background-color: transparent;
    z-index: 1; /* Ensure it's above other content */
}

/* iOS PWA specific adjustments */
.ios-pwa-map {
    top: 0 !important; /* Start from the very top */
    height: 100vh !important; /* Take full viewport height */
    padding-top: 48px; /* Add padding for the navbar */
    padding-bottom: 56px; /* Add padding for the bottom navbar */
}

/* Global iOS PWA mode adjustments */
html.ios-pwa-mode .venue-map-container {
    top: 0 !important;
    height: 100vh !important;
    padding-top: 48px;
    padding-bottom: 56px;
}

html.ios-pwa-mode .map-container {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    height: 100%;
    width: 100%;
}

/* Map container to position controls */
.map-container {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

/* Loading and error messages */
.venue-map-container p {
    margin: 20px auto;
    text-align: center;
}

.venue-map-container .error {
    color: var(--danger);
    background-color: #f8d7da;
    border-radius: 4px;
    padding: 10px 15px;
    border-left: 4px solid var(--danger);
}

/* InfoWindow Styles */
.venue-info {
    padding: 12px;
    width: 320px; /* Increased from 280px to 320px */
    max-height: none;
    overflow-y: visible;
    overflow-x: hidden;
    box-sizing: border-box;
    max-width: 100%;
    background-color: var(--card-bg);
    color: var(--text-dark);
}

.venue-info-title {
    margin: 0 0 12px 0;
    font-size: 1.1rem;
    color: var(--primary-bg);
    border-bottom: 1px solid var(--neutral-lighter);
    padding-bottom: 8px;
    text-align: center;
    word-wrap: break-word;
    overflow-wrap: break-word;
    hyphens: auto;
}

.venue-upcoming-events {
    margin: 0 0 8px 0;
    font-size: 0.9rem;
    width: 100%;
}

.venue-event-list {
    list-style: none;
    padding: 0;
    margin: 0;
    max-height: none;
    overflow-y: visible;
    overflow-x: hidden;
    width: 100%;
}

.venue-event-item {
    margin-bottom: 8px;
    padding-bottom: 8px;
    border-bottom: 1px solid var(--neutral-lighter);
    word-break: break-word;
    width: 100%;
}

.venue-event-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.venue-event-row {
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    width: 100%;
}

.venue-event-date {
    font-weight: 600;
    color: var(--primary-bg);
    font-size: 0.9rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    min-width: 100px;
    flex-shrink: 0;
    margin-right: 8px;
}

.venue-event-title {
    font-size: 0.95rem;
    color: var(--text-dark);
    line-height: 1.3;
    overflow: hidden;
    text-overflow: ellipsis;
    flex: 1;
}

.venue-view-button {
    margin-top: 12px;
    width: 100%;
    font-size: 0.95rem;
    padding: 8px 12px;
    font-weight: 500;
    box-sizing: border-box;
    white-space: nowrap;
}

/* Google Maps InfoWindow overrides */
.gm-style .gm-style-iw-c {
    padding: 0 !important;
    border-radius: 8px !important;
    max-width: 350px !important; /* Increased from 300px to 350px */
    overflow: visible !important;
    background-color: var(--card-bg) !important;
}

.gm-style .gm-style-iw-d {
    overflow: visible !important;
    padding: 0 !important;
    max-width: 350px !important; /* Increased from 300px to 350px */
    max-height: none !important;
    background-color: var(--card-bg) !important;
}

/* Dark mode specific InfoWindow styling */
[data-theme="dark"] .gm-style .gm-style-iw-t::after {
    background: var(--card-bg) !important;
}

/* Fix for Google Maps InfoWindow scrollbars */
.gm-style-iw.gm-style-iw-c > button {
    display: block !important;
    top: 0 !important;
    right: 0 !important;
}

/* Dark mode InfoWindow close button */
[data-theme="dark"] .gm-style-iw.gm-style-iw-c > button > img {
    filter: invert(1) !important;
}

/* Location button styles */
.location-button {
    position: absolute;
    bottom: 10px; /* Moved lower (from 20px to 10px) */
    left: 10px;
    z-index: 10;
    background-color: var(--card-bg);
    border: none;
    border-radius: 2px;
    box-shadow: 0 1px 4px var(--shadow-color);
    cursor: pointer;
    margin: 10px;
    padding: 0;
    width: 40px;
    height: 40px;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
}

.location-button i {
    color: var(--text-medium);
    font-size: 18px;
}

.location-button:hover {
    background-color: var(--bg-secondary);
}

.location-button:active {
    background-color: var(--bg-tertiary);
}

/* Location error message */
.location-error {
    position: absolute;
    bottom: 70px; /* Adjusted to maintain distance from the lowered button */
    left: 10px;
    z-index: 10;
    background-color: rgba(220, 53, 69, 0.1);
    color: var(--danger);
    padding: 8px 12px;
    border-radius: 4px;
    box-shadow: 0 1px 4px var(--shadow-color);
    max-width: 300px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 0.9rem;
}

.close-error {
    background: none;
    border: none;
    color: var(--danger);
    font-size: 1.2rem;
    cursor: pointer;
    padding: 0 0 0 8px;
    margin-left: 8px;
}

/* Mobile optimizations */
@media (max-width: 768px) {
    .venue-map-container {
        position: fixed;
        height: calc(100vh - 48px - 56px); /* Adjust for top navbar and bottom navbar */
        top: 48px; /* Position right below top navbar */
        left: 0;
        right: 0;
        bottom: 56px; /* Position right above bottom navbar */
        margin: 0;
        padding: 0;
        display: flex;
        flex-direction: column;
        z-index: 1; /* Ensure it's above other content */
    }

    /* iOS PWA specific adjustments for mobile */
    .ios-pwa-map {
        top: 0 !important; /* Start from the very top */
        height: 100vh !important; /* Take full viewport height */
        padding-top: calc(48px + env(safe-area-inset-top, 0px)); /* Add padding for the navbar and status bar */
        padding-bottom: calc(56px + env(safe-area-inset-bottom, 0px)); /* Add padding for the bottom navbar and home indicator */
    }

    /* Global iOS PWA mode adjustments for mobile */
    html.ios-pwa-mode .venue-map-container {
        top: 0 !important;
        height: 100vh !important;
        padding-top: calc(48px + env(safe-area-inset-top, 0px));
        padding-bottom: calc(56px + env(safe-area-inset-bottom, 0px));
    }

    html.ios-pwa-mode .map-container {
        height: 100%;
        width: 100%;
        overflow: hidden;
    }

    /* Ensure map container respects bottom navbar */
    .map-container {
        height: 100%;
        width: 100%;
        overflow: hidden;
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
    }

    /* Ensure the Google Map fills the entire container */
    .venue-map-container > div {
        flex: 1;
        height: 100%;
        min-height: 100%;
    }

    /* Fix for Google Maps container */
    .venue-map-container .gm-style {
        position: absolute;
        height: 100%;
        width: 100%;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
    }

    /* Adjust InfoWindow size for mobile */
    .venue-info {
        width: 320px; /* Increased from 280px to 320px */
        max-height: none;
        overflow-y: visible;
        overflow-x: hidden;
    }

    /* Adjust Google Maps InfoWindow for mobile */
    .gm-style .gm-style-iw-c {
        max-width: 350px !important; /* Increased from 300px to 350px */
    }

    .gm-style .gm-style-iw-d {
        max-width: 350px !important; /* Increased from 300px to 350px */
    }

    /* Ensure text is readable on smaller screens */
    .venue-event-title {
        font-size: 0.9rem;
    }

    .venue-event-date {
        font-size: 0.85rem;
        min-width: 90px; /* Slightly smaller on mobile */
    }

    /* Ensure the row layout works well on mobile */
    .venue-event-row {
        align-items: flex-start;
    }

    /* Adjust location button for mobile */
    .location-button {
        bottom: 60px; /* Moved lower (from 70px to 60px) but still above the bottom navbar */
        left: 5px;
        margin: 5px;
        width: 36px;
        height: 36px;
    }

    .location-error {
        bottom: 110px; /* Adjusted to maintain distance from the lowered button */
        left: 5px;
        max-width: 250px;
        font-size: 0.8rem;
    }
}
