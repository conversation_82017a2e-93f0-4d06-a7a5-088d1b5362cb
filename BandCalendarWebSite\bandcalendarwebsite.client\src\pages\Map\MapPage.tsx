import React, { useState, useEffect, CSSProperties } from 'react';
import { useNavigate } from 'react-router-dom';
import { GoogleMap, InfoWindow, Marker } from '@react-google-maps/api';
import { format, parseISO } from 'date-fns';
import { useGoogleMaps } from '../../contexts/GoogleMapsContext.tsx';
import BandCalendarApiService, { CalendarEvent } from '../../services/BandCalendarApiService.ts';
import geocodingService from '../../services/GeocodingService.ts';
import './MapPage.css';

// Define an interface for venue data with coordinates
interface VenueData {
    name: string;
    position: {
        lat: number;
        lng: number;
    };
    gigCount: number;
    events: CalendarEvent[];
}

// Default center (can be adjusted based on venues)
const defaultCenter = {
    lat: 40.0583, // Default to New Jersey coordinates
    lng: -74.4057
};

// Interface for the raw venue data before geocoding
interface RawVenueData {
    name: string;
    events: CalendarEvent[];
}


interface VenueMapProps {
    isIOS?: boolean;
    isPWA?: boolean;
}

const MapPage: React.FC<VenueMapProps> = ({ isIOS: propIsIOS, isPWA: propIsPWA }) => {
    // Determine if we're running as a PWA on iOS
    // Use props if provided, otherwise detect automatically
    const isIOS = propIsIOS !== undefined ? propIsIOS :
                  (/iPad|iPhone|iPod/.test(navigator.userAgent) && !(window as any).MSStream);
    const isPWA = propIsPWA !== undefined ? propIsPWA :
                  (window.matchMedia('(display-mode: standalone)').matches ||
                  (window.navigator as any).standalone === true);
    const [venues, setVenues] = useState<VenueData[]>([]);
    const [rawVenues, setRawVenues] = useState<RawVenueData[]>([]);
    const [selectedVenue, setSelectedVenue] = useState<VenueData | null>(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [map, setMap] = useState<google.maps.Map | null>(null);
    const [advancedMarkers, setAdvancedMarkers] = useState<google.maps.marker.AdvancedMarkerElement[]>([]);
    const [mapCenter, setMapCenter] = useState(defaultCenter);
    const [mapZoom] = useState(8);
    const [userLocation, setUserLocation] = useState<google.maps.LatLngLiteral | null>(null);
    const [showUserLocation, setShowUserLocation] = useState(false);
    const [locationError, setLocationError] = useState<string | null>(null);
    const [isDarkMode, setIsDarkMode] = useState(false);
    const navigate = useNavigate();

    // Use the Google Maps context
    const { isLoaded, loadError } = useGoogleMaps();

    // Fetch events once the Maps API is loaded
    useEffect(() => {
        if (isLoaded) {
            fetchEventData();
        }
    }, [isLoaded]);

    // Process venues after events are loaded and maps is ready
    useEffect(() => {
        if (isLoaded && rawVenues.length > 0) {
            geocodeVenues(rawVenues);
        }
    }, [isLoaded, rawVenues]);

    // Create advanced markers when venues are ready and map is available
    useEffect(() => {
        if (map && venues.length > 0) {
            createAdvancedMarkers();
        }
    }, [map, venues]);

    // Handle load error
    useEffect(() => {
        if (loadError) {
            console.log("Failed to load Google Maps", loadError);
            setError('Failed to load Google Maps. Please try again later.');
            setLoading(false);
        }
    }, [loadError]);

    // Handle iOS PWA mode
    useEffect(() => {
        // Add a class to the document body for iOS PWA mode
        if (isIOS && isPWA) {
            document.documentElement.classList.add('ios-pwa-mode');
        } else {
            document.documentElement.classList.remove('ios-pwa-mode');
        }

        return () => {
            document.documentElement.classList.remove('ios-pwa-mode');
        };
    }, [isIOS, isPWA]);

    // Detect dark mode
    useEffect(() => {
        // Check if dark mode is active
        const checkDarkMode = () => {
            const isDark = document.documentElement.getAttribute('data-theme') === 'dark';
            setIsDarkMode(isDark);

            // If map exists, update the map type
            if (map) {
                // Force map redraw by triggering a resize event
                window.dispatchEvent(new Event('resize'));
            }
        };

        // Initial check
        checkDarkMode();

        // Create a MutationObserver to watch for theme changes
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.attributeName === 'data-theme') {
                    checkDarkMode();
                }
            });
        });

        // Start observing the document element for data-theme attribute changes
        observer.observe(document.documentElement, { attributes: true });

        // Cleanup
        return () => {
            observer.disconnect();
        };
    }, [map]);

    const fetchEventData = async () => {
        setLoading(true);
        setError(null);

        try {
            // Get date range for the next year
            const startDate = format(new Date(), 'yyyy-MM-dd');
            const endDate = format(new Date(new Date().setFullYear(new Date().getFullYear() + 1)), 'yyyy-MM-dd');

            // Fetch all events
            const events = await BandCalendarApiService.getGigEvents(startDate, endDate, null);

            // Group events by venue and count them
            const venueMap = new Map<string, CalendarEvent[]>();

            events.forEach(event => {
                if (event.location) {
                    if (!venueMap.has(event.location)) {
                        venueMap.set(event.location, []);
                    }
                    venueMap.get(event.location)?.push(event);
                }
            });

            // Create raw venues data to be geocoded later
            const rawVenuesData = Array.from(venueMap.entries()).map(([venueName, venueEvents]) => ({
                name: venueName,
                events: venueEvents
            }));

            setRawVenues(rawVenuesData);
        } catch (err) {
            console.error('Error fetching venue data:', err);
            setError('Failed to load venue data. Please try again later.');
            setLoading(false);
        }
    };

    const geocodeVenues = async (rawVenues: RawVenueData[]) => {
        try {
            // Convert venue names to coordinates using our shared geocoding service
            const venueDataPromises = rawVenues.map(async (venue) => {
                try {
                    // Use our shared geocoding service
                    const coordinates = await geocodingService.geocodeAddress(venue.name);

                    if (coordinates) {
                        return {
                            name: venue.name,
                            position: coordinates, // Already in the correct format {lat, lng}
                            gigCount: venue.events.length,
                            events: venue.events
                        };
                    } else {
                        // If geocoding fails, use a default position with slight offset
                        const randomOffset = (Math.random() - 0.5) * 0.01;
                        console.warn(`Geocoding failed for venue: ${venue.name}. Using default position.`);
                        return {
                            name: venue.name,
                            position: {
                                lat: defaultCenter.lat + randomOffset,
                                lng: defaultCenter.lng + randomOffset
                            },
                            gigCount: venue.events.length,
                            events: venue.events
                        };
                    }
                } catch (error) {
                    console.error(`Error geocoding venue ${venue.name}:`, error);
                    // Return a default position with a random offset
                    const randomOffset = (Math.random() - 0.5) * 0.01;
                    return {
                        name: venue.name,
                        position: {
                            lat: defaultCenter.lat + randomOffset,
                            lng: defaultCenter.lng + randomOffset
                        },
                        gigCount: venue.events.length,
                        events: venue.events
                    };
                }
            });

            const venueData = await Promise.all(venueDataPromises);
            setVenues(venueData);

            // Calculate optimal map center and zoom based on venue positions
            if (venueData.length > 0) {
                calculateMapBounds(venueData);
            }
        } catch (err) {
            console.error('Error geocoding venues:', err);
            setError('Failed to process venue locations. Please try again later.');
        } finally {
            setLoading(false);
        }
    };

    // Calculate the optimal map center and zoom level based on venue positions
    const calculateMapBounds = (venues: VenueData[]) => {
        if (venues.length === 0) return;

        // Create a bounds object
        const bounds = new google.maps.LatLngBounds();

        // Add each venue position to the bounds
        venues.forEach(venue => {
            bounds.extend(venue.position);
        });

        // Get the center of the bounds
        const center = bounds.getCenter();
        setMapCenter({ lat: center.lat(), lng: center.lng() });

        // We'll let the map auto-zoom based on bounds when it loads
        if (map) {
            map.fitBounds(bounds);

            // Add minimal padding to the bounds
            // Wait for bounds to be applied, then adjust zoom for tighter framing
            google.maps.event.addListenerOnce(map, 'bounds_changed', () => {
                // Get current zoom and adjust for tighter framing
                const currentZoom = map.getZoom();
                if (currentZoom !== undefined) {
                    // Zoom in slightly for tighter framing (0.2 provides minimal padding)
                    map.setZoom(currentZoom + 0.2);
                }
            });
        }
    };

    const createAdvancedMarkers = () => {
        // Remove any existing markers
        advancedMarkers.forEach(marker => marker.map = null);

        // Create new advanced markers for each venue
        const newMarkers = venues.map((venue) => {
            // Create a pin element for the marker
            const pinElement = document.createElement('div');
            pinElement.className = 'venue-marker';
            pinElement.innerHTML = `<div class="marker-count">${venue.gigCount}</div>`;

            // Set marker size based on gig count (larger for more gigs)
            const baseSize = 30;
            const sizeIncrease = Math.min(venue.gigCount * 2, 20); // Cap the size increase
            const markerSize = baseSize + sizeIncrease;

            // Apply styles to the marker
            pinElement.style.backgroundColor = 'var(--accent)';  // Use accent color from theme
            pinElement.style.color = 'var(--text-on-accent)';
            pinElement.style.borderRadius = '50%';
            pinElement.style.padding = '8px';
            pinElement.style.display = 'flex';
            pinElement.style.justifyContent = 'center';
            pinElement.style.alignItems = 'center';
            pinElement.style.fontWeight = 'bold';
            pinElement.style.fontSize = '14px';
            pinElement.style.width = `${markerSize}px`;
            pinElement.style.height = `${markerSize}px`;
            pinElement.style.cursor = 'pointer';
            pinElement.style.boxShadow = isDarkMode ? '0 2px 6px rgba(0,0,0,0.5)' : '0 2px 6px rgba(0,0,0,0.3)';
            pinElement.style.border = isDarkMode ? '2px solid var(--bg-main)' : '2px solid white';

            // Create AdvancedMarkerElement
            const marker = new google.maps.marker.AdvancedMarkerElement({
                position: venue.position,
                content: pinElement,
                map: map,
                title: venue.name
            });

            // Add click event listener
            marker.addListener('click', () => {
                handleMarkerClick(venue);
            });

            return marker;
        });

        setAdvancedMarkers(newMarkers);
    };

    const handleMarkerClick = (venue: VenueData) => {
        setSelectedVenue(venue);

        // Center the map on the clicked venue and zoom out for better context
        if (map) {
            // Set a lower zoom level to provide more context around the venue
            // and make room for the larger popup
            const currentZoom = map.getZoom() || 8;
            const newZoom = Math.min(currentZoom, 10); // Lower zoom level (from 11 to 10) for better context

            map.setZoom(newZoom);

            // Pan to a position slightly offset to account for the InfoWindow
            // This helps ensure the InfoWindow is fully visible
            const isMobile = window.innerWidth <= 768;
            const offset = isMobile ? 0.001 : 0.0005; // Smaller offset on desktop

            const adjustedPosition = {
                lat: venue.position.lat - offset,
                lng: venue.position.lng
            };

            map.panTo(adjustedPosition);
        }
    };

    const handleInfoWindowClose = () => {
        setSelectedVenue(null);
    };

    const navigateToEvents = (venueName: string) => {
        // Navigate to Events view with venue filter in query string
        navigate(`/events?location=${encodeURIComponent(venueName)}`);
    };

    // Handle map instance created
    const handleMapLoad = (mapInstance: google.maps.Map) => {
        setMap(mapInstance);
    };

    // Get user's current location
    const getUserLocation = () => {
        setLocationError(null);

        if (navigator.geolocation) {
            navigator.geolocation.getCurrentPosition(
                (position) => {
                    const userPos = {
                        lat: position.coords.latitude,
                        lng: position.coords.longitude
                    };

                    setUserLocation(userPos);
                    setShowUserLocation(true);

                    // Center map on user location
                    if (map) {
                        map.setCenter(userPos);
                        map.setZoom(12); // Zoom in to a reasonable level
                    }
                },
                (error) => {
                    console.error('Error getting user location:', error);
                    setLocationError('Unable to get your location. Please check your browser permissions.');
                    setShowUserLocation(false);
                },
                {
                    enableHighAccuracy: true,
                    timeout: 5000,
                    maximumAge: 0
                }
            );
        } else {
            setLocationError('Geolocation is not supported by your browser.');
            setShowUserLocation(false);
        }
    };

    if (error) {
        return (
            <div className="venue-map-container">
                <p className="error">{error}</p>
            </div>
        );
    }

    // Add iOS PWA specific class if needed
    const getMapContainerClass = () => {
        let classes = ['venue-map-container'];
        if (isIOS && isPWA) classes.push('ios-pwa-map');
        return classes.join(' ');
    };

    // Adjust container style for iOS PWA if needed
    const getMapContainerStyle = (): CSSProperties => {
        // For all devices, use 100% height and width to fill the container
        const style: CSSProperties = {
            width: '100%',
            height: '100%',
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            margin: 0,
            padding: 0,
            borderRadius: 0,
            overflow: 'hidden'
        };

        return style;
    };

    return (
        <div className={getMapContainerClass()}>
            {!isLoaded || loading ? (
                <p>Loading venue data...</p>
            ) : (
                <div className="map-container">
                    <button
                        className="location-button"
                        onClick={getUserLocation}
                        title="Show my location"
                    >
                        <i className="bi bi-geo"></i>
                    </button>

                    {locationError && (
                        <div className="location-error">
                            {locationError}
                            <button className="close-error" onClick={() => setLocationError(null)}>×</button>
                        </div>
                    )}

                    <GoogleMap
                        mapContainerStyle={getMapContainerStyle()}
                        center={mapCenter}
                        zoom={mapZoom}
                        onLoad={handleMapLoad}
                        options={{
                            mapId: "37b1c040e90dda6e", 
                            colorScheme: isDarkMode ? 'DARK' : 'LIGHT',
                            fullscreenControl: true,
                            streetViewControl: false,
                            mapTypeControl: true,
                            zoomControl: true,
                            gestureHandling: 'greedy' // Allows one-finger panning on mobile devices
                        }}
                    >
                        {/* User location marker */}
                        {showUserLocation && userLocation && (
                            <Marker
                                position={userLocation}
                                icon={{
                                    path: google.maps.SymbolPath.CIRCLE,
                                    scale: 10,
                                    fillColor: '#4285F4',
                                    fillOpacity: 1,
                                    strokeColor: isDarkMode ? 'var(--bg-main)' : 'white',
                                    strokeWeight: 2,
                                }}
                                title="Your Location"
                            />
                        )}

                        {selectedVenue && (
                            <InfoWindow
                                position={selectedVenue.position}
                                onCloseClick={handleInfoWindowClose}
                                options={{
                                    pixelOffset: new google.maps.Size(0, -5),
                                    maxWidth: 350, // Increased from 300 to 350 for more space
                                    disableAutoPan: false
                                }}
                            >
                                <div className="venue-info">
                                    <h5 className="venue-info-title">{selectedVenue.name}</h5>

                                    {selectedVenue.events.length > 0 && (
                                        <div className="venue-upcoming-events">
                                            <ul className="venue-event-list">
                                                {selectedVenue.events
                                                    .sort((a, b) => new Date(a.startTime).getTime() - new Date(b.startTime).getTime())
                                                    .map((event, index) => (
                                                        <li key={index} className="venue-event-item">
                                                            <div className="venue-event-row">
                                                                <span className="venue-event-date">
                                                                    {format(parseISO(event.startTime), 'MMM d, yyyy')}
                                                                </span>
                                                                <span className="venue-event-title">{event.summary}</span>
                                                            </div>
                                                        </li>
                                                    ))
                                                }
                                            </ul>
                                        </div>
                                    )}

                                    <button
                                        className="btn btn-sm btn-primary venue-view-button"
                                        onClick={() => navigateToEvents(selectedVenue.name)}
                                    >
                                        View All Events
                                    </button>
                                </div>
                            </InfoWindow>
                        )}
                    </GoogleMap>
                </div>
            )}
        </div>
    );
};

export default MapPage;
