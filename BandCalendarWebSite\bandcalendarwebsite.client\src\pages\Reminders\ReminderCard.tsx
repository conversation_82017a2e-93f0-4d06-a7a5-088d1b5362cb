import React, {useEffect, useState} from "react";
import Swipeable from "../../components/Swipeable.tsx";
import {Reminder} from "./types/Reminder.tsx";

interface ReminderCardProps {
    reminder: Reminder;
    deleteReminder: (reminderId: string) => Promise<void>;
    deleting: string | null;
    isOnline: boolean;
}

// Reminder Card Component
export const ReminderCard: React.FC<ReminderCardProps> = ({reminder, deleteReminder, deleting, isOnline}) => {
    const [isOpen, setIsOpen] = useState(false);
    const [isMobileView, setIsMobileView] = useState(window.innerWidth < 768);

    // Update isMobileView when window resizes
    useEffect(() => {
        const handleResize = () => {
            setIsMobileView(window.innerWidth < 768);
        };

        window.addEventListener('resize', handleResize);
        return () => window.removeEventListener('resize', handleResize);
    }, []);

    const handleDelete = () => {
        if (reminder.reminderId) {
            deleteReminder(reminder.reminderId);
            setIsOpen(false);
        }
    };

    const canDelete = (reminder.alreadyExists || reminder.created || reminder.isOutdated || reminder.isOld);

    return (
        <div
            className={`swipeable-card-container ${isMobileView ? 'mobile-view' : 'desktop-view'} ${canDelete ? 'can-delete' : ''}`}>
            {isMobileView ? (
                <Swipeable
                    onSwipedLeft={() => {
                        if (canDelete && isOnline) {
                            setIsOpen(true);
                        }
                    }}
                    onSwipedRight={() => {
                        setIsOpen(false);
                    }}
                    className={`reminder-card ${reminder.isOld ? 'old-reminder' : ''} ${
                        reminder.isOutdated || (!reminder.alreadyExists && !reminder.created && !reminder.isOld)
                            ? 'needs-attention-card'
                            : ''
                    } ${isOpen && isMobileView ? 'swiped-open' : ''}`}
                >
                    <div className="reminder-header">
                        <div className="reminder-title-container">
                            <div className="reminder-title">
                                {reminder.title}
                            </div>
                        </div>
                        <div className="reminder-time">{reminder.displayTime}</div>
                    </div>

                    <div className="reminder-description">{reminder.description}</div>

                    {/* Only show footer if there are status badges to display */}
                    {(reminder.isOld || reminder.isOutdated || !reminder.alreadyExists) && (
                        <div className="reminder-footer">
                            <div className="reminder-status">
                                {reminder.isOld && (
                                    <span
                                        className="status-badge old-reminder-badge"
                                        title="This reminder is for a date that has already passed."
                                    >
                                        Date has passed
                                    </span>
                                )}
                                {reminder.isOutdated && (
                                    <span
                                        className="status-badge outdated-badge"
                                        title={reminder.outdatedReason || 'This reminder is outdated and needs to be recreated.'}
                                    >
                                        ⚠️ Outdated
                                    </span>
                                )}
                                {/* Removed "Exists" badge to make layout more compact */}
                                {!reminder.alreadyExists && (
                                    reminder.created ? (
                                        <span className="status-badge created-badge">Created</span>
                                    ) : (
                                        <span className="status-badge not-created-badge">Not Created</span>
                                    )
                                )}
                            </div>
                        </div>
                    )}
                </Swipeable>
            ) : (
                <div className={`reminder-card ${reminder.isOld ? 'old-reminder' : ''} ${
                    reminder.isOutdated || (!reminder.alreadyExists && !reminder.created && !reminder.isOld)
                        ? 'needs-attention-card'
                        : ''
                }`}>
                    <div className="reminder-header">
                        <div className="reminder-title-container">
                            <div className="reminder-title">
                                {reminder.title}
                            </div>

                            {/* Desktop delete button in top right corner */}
                            {canDelete && (
                                <button
                                    onClick={handleDelete}
                                    disabled={deleting === reminder.reminderId || !isOnline}
                                    className={`desktop-delete-button corner-button ${!isOnline ? 'offline-disabled' : ''}`}
                                    title={isOnline ? "Delete this reminder" : "Cannot delete reminders while offline"}
                                >
                                    <i className="bi bi-trash"></i>
                                </button>
                            )}
                        </div>
                        <div className="reminder-time">{reminder.displayTime}</div>
                    </div>

                    <div className="reminder-description">{reminder.description}</div>

                    {/* Only show footer if there are status badges to display */}
                    {(reminder.isOld || reminder.isOutdated || !reminder.alreadyExists) && (
                        <div className="reminder-footer">
                            <div className="reminder-status">
                                {reminder.isOld && (
                                    <span
                                        className="status-badge old-reminder-badge"
                                        title="This reminder is for a date that has already passed."
                                    >
                                        Date has passed
                                    </span>
                                )}
                                {reminder.isOutdated && (
                                    <span
                                        className="status-badge outdated-badge"
                                        title={reminder.outdatedReason || 'This reminder is outdated and needs to be recreated.'}
                                    >
                                        ⚠️ Outdated
                                    </span>
                                )}
                                {/* Removed "Exists" badge to make layout more compact */}
                                {!reminder.alreadyExists && (
                                    reminder.created ? (
                                        <span className="status-badge created-badge">Created</span>
                                    ) : (
                                        <span className="status-badge not-created-badge">Not Created</span>
                                    )
                                )}
                            </div>
                        </div>
                    )}
                </div>
            )}

            {/* Mobile swipe delete button */}
            {isMobileView && canDelete && (
                <div className="swipe-delete-button">
                    <button
                        onClick={handleDelete}
                        disabled={deleting === reminder.reminderId || !isOnline}
                        className={`delete-button ${!isOnline ? 'offline-disabled' : ''}`}
                        title={isOnline ? "Delete this reminder" : "Cannot delete reminders while offline"}
                    >
                        <i className="bi bi-trash"></i>
                    </button>
                </div>
            )}
        </div>
    );
};