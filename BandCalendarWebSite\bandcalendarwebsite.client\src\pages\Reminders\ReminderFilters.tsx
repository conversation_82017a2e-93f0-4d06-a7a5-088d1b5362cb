import React from 'react';

interface ReminderFiltersProps {
    startDate: string;
    endDate: string;
    setStartDate: (value: string) => void;
    setEndDate: (value: string) => void;
    fetchAllReminders: () => void;
    loading: boolean;
}

const ReminderFilters: React.FC<ReminderFiltersProps> = ({
    startDate,
    endDate,
    setStartDate,
    setEndDate,
    fetchAllReminders,
    loading
}) => {
    return (
        <div className="compact-filters">
            <div className="filter-content">
                {/* Start Date on its own row */}
                <div className="filter-row">
                    <div className="date-filter" style={{ width: '100%' }}>
                        <label>Start Date</label>
                        <input
                            type="date"
                            value={startDate}
                            onChange={(e) => setStartDate(e.target.value)}
                        />
                    </div>
                </div>

                {/* End Date on its own row */}
                <div className="filter-row">
                    <div className="date-filter" style={{ width: '100%' }}>
                        <label>End Date</label>
                        <input
                            type="date"
                            value={endDate}
                            onChange={(e) => setEndDate(e.target.value)}
                        />
                    </div>
                </div>

                <div className="filter-actions" style={{ borderBottom: 'none' }}>
                    <button
                        onClick={fetchAllReminders}
                        disabled={loading}
                    >
                        <i className="bi bi-search me-1"></i>
                        Preview
                    </button>
                </div>
            </div>
        </div>
    );
};

export default ReminderFilters;