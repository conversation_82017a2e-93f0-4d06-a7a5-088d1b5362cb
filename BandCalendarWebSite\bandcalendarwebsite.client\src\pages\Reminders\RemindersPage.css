/* Reminders Component Styles */

/* Filter bar styling - Mobile-first approach */
.filters {
    display: flex;
    flex-direction: column;
    gap: 10px;
    background-color: var(--bg-secondary);
    padding: 12px;
    border-radius: 8px;
    align-items: stretch;
    margin-bottom: 15px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.08);
}

.date-filter {
    flex: 1;
}

.date-filter label {
    display: block;
    margin-bottom: 4px;
    font-weight: 500;
    color: var(--text-medium);
    font-size: 0.85rem;
}

.date-filter input {
    width: 100%;
    padding: 10px;
    border: 1px solid var(--neutral-lighter);
    border-radius: 6px;
    background-color: var(--card-bg);
    color: var(--text-dark);
    font-size: 16px; /* Prevents iOS zoom on focus */
}

.button-container {
    width: 100%;
    margin-top: 5px;
}

.button-container button {
    width: 100%;
}

/* Action buttons styling - Mobile-first */
.action-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 15px;
    background-color: var(--bg-secondary);
    padding: 12px;
    border-radius: 8px;
    box-shadow: 0 1px 3px var(--shadow-color);
}

.action-buttons button {
    flex: 1;
    min-width: 120px;
    white-space: nowrap;
}

/* Compact filters for mobile */
.compact-filters {
    background-color: var(--bg-secondary);
    padding: 10px 12px;
    border-radius: 8px;
    margin-bottom: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
}

.filter-content {
    display: flex;
    flex-direction: column;
    gap: 12px;
    padding: 8px;
}

.filter-row {
    display: flex;
    width: 100%;
    gap: 12px;
}

.filter-actions {
    display: flex;
    justify-content: flex-end;
    margin-top: 12px;
}

.filter-content button {
    height: 40px;
    padding: 0 16px;
    font-size: 0.9rem;
    background-color: var(--primary-bg);
    color: var(--text-on-primary);
    border: none;
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.filter-content button i {
    margin-right: 6px;
}

.filter-content button:hover {
    background-color: var(--primary-darker);
}

.filter-content button:disabled {
    opacity: 0.7;
    cursor: not-allowed;
}

/* Desktop styles */
@media (min-width: 768px) {
    .filters {
        padding: 15px;
    }

    .filter-grid {
        display: flex;
        align-items: flex-start;
        gap: 15px;
        flex-wrap: wrap;
    }

    .filter-section {
        display: flex;
        min-width: 150px;
    }

    .filter-section.button-section {
        margin-left: auto;
    }

    .refresh-button {
        height: 38px;
        padding: 0 16px;
        font-size: 0.9rem;
        background-color: var(--primary-bg);
        color: var(--text-on-primary);
        border: none;
        border-radius: 6px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .refresh-button:hover {
        background-color: var(--primary-darker);
    }

    .refresh-button:disabled {
        opacity: 0.7;
        cursor: not-allowed;
    }

    .action-buttons {
        padding: 15px;
    }
}

.btn {
    padding: 8px 16px;
    border-radius: 6px;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    box-shadow: 0 1px 2px rgba(0,0,0,0.1);
    height: 38px;
}

.btn i {
    margin-right: 6px;
    font-size: 1rem;
}

.btn-primary {
    background-color: var(--primary-bg);
    border-color: var(--primary-bg);
    color: white;
}

.btn-primary:hover {
    background-color: var(--primary-darker);
    border-color: var(--primary-darker);
}

.btn-success {
    background-color: #28a745;
    border-color: #28a745;
    color: white;
}

.btn-success:hover {
    background-color: #218838;
    border-color: #1e7e34;
}

.btn-danger {
    background-color: #dc3545;
    border-color: #dc3545;
    color: white;
}

.btn-danger:hover {
    background-color: #c82333;
    border-color: #bd2130;
}

.btn-outline-primary {
    background-color: transparent;
    border: 1px solid var(--primary-bg);
    color: var(--primary-bg);
}

.btn-outline-primary:hover {
    background-color: var(--primary-bg);
    color: white;
}

/* Status messages */
.status-message {
    padding: 12px 15px;
    border-radius: 6px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    font-weight: 500;
}

.status-message i {
    margin-right: 8px;
    font-size: 1.1rem;
}

.status-message.loading {
    background-color: rgba(23, 162, 184, 0.1);
    color: var(--info);
    border-left: 4px solid var(--info);
}

.status-message.creating {
    background-color: rgba(40, 167, 69, 0.1);
    color: var(--success);
    border-left: 4px solid var(--success);
}

.status-message.error {
    background-color: rgba(220, 53, 69, 0.1);
    color: var(--danger);
    border-left: 4px solid var(--danger);
}

/* Mobile-optimized card layout for reminders */
.reminders-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-bottom: 20px;
    padding: 4px;
}

/* Swipeable card container */
.swipeable-card-container {
    position: relative;
    overflow: hidden;
    border-radius: 10px;
    box-shadow: 0 2px 8px var(--shadow-color);
}

/* Add a subtle hint for swipe functionality - only on mobile and only for deletable reminders */
.swipeable-card-container.mobile-view.can-delete::after {
    content: "";
    position: absolute;
    top: 50%;
    right: 10px;
    transform: translateY(-50%);
    width: 4px;
    height: 25px;
    background-color: var(--shadow-color);
    border-radius: 2px;
    z-index: 2;
    pointer-events: none;
}

.reminder-card {
    background-color: var(--card-bg);
    padding: 14px;
    display: flex;
    flex-direction: column;
    gap: 8px;
    transition: transform 0.3s ease;
    z-index: 1;
    position: relative;
    color: var(--text-dark);
}

.reminder-card.swiped-open {
    transform: translateX(-80px);
}

.swipe-delete-button {
    position: absolute;
    top: 0;
    right: 0;
    height: 100%;
    width: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #dc3545;
    z-index: 0;
}

.swipe-delete-button .delete-button {
    background-color: transparent;
    border: none;
    color: white;
    font-size: 1.5rem;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
}

.swipe-delete-button .delete-button:hover {
    background-color: #c82333;
}

.reminder-header {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.reminder-title-container {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    width: 100%;
}

.reminder-title {
    font-weight: 600;
    font-size: 1rem;
    color: var(--primary-bg);
    display: flex;
    align-items: center;
    gap: 4px;
    flex: 1;
    padding-right: 8px;
}

.reminder-time {
    font-size: 0.9rem;
    color: var(--text-medium);
}

.reminder-description {
    font-size: 0.9rem;
    color: var(--text-medium);
    margin: 6px 0;
    line-height: 1.4;
}

.reminder-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 4px;
    flex-wrap: wrap;
    gap: 6px;
}

/* Desktop view specific styles */
.desktop-view .reminder-footer {
    justify-content: space-between;
}

.reminder-status {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
}

.status-badge {
    font-size: 0.75rem;
    font-weight: 500;
    padding: 4px 8px;
    border-radius: 12px;
    display: inline-flex;
    align-items: center;
}

.outdated-badge {
    background-color: #f8d7da;
    color: #842029;
}

.deleted-badge {
    background-color: #6c757d;
    color: white;
}

.exists-badge {
    background-color: #d1e7dd;
    color: #0f5132;
}

.created-badge {
    background-color: #d1e7dd;
    color: #0f5132;
}

.not-created-badge {
    background-color: #cff4fc;
    color: #055160;
}

.delete-button {
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 6px;
    padding: 6px 12px;
    font-size: 0.8rem;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s;
}

.delete-button:hover {
    background-color: #c82333;
}

.delete-button:disabled {
    background-color: #e9a8ae;
    cursor: not-allowed;
}

/* Desktop delete button styling */
.desktop-delete-button {
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 6px;
    padding: 6px 10px;
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Corner button specific styling */
.desktop-delete-button.corner-button {
    padding: 6px 8px;
    border-radius: 6px;
    background-color: rgba(220, 53, 69, 0.9);
    margin-left: 8px;
    margin-top: -2px;
}

.desktop-delete-button:hover {
    background-color: #c82333;
    transform: scale(1.05);
}

.desktop-delete-button:disabled {
    background-color: #e9a8ae;
    cursor: not-allowed;
}

.desktop-delete-button i {
    font-size: 1rem;
}

.corner-button i {
    font-size: 0.95rem;
}

/* Old reminder styling for card layout */
.old-reminder {
    background-color: #f8f9fa;
    background-color: var(--past-event-bg) !important;
    color: var(--past-event-text) !important;
}

.old-reminder .reminder-title,
.old-reminder .reminder-time,
.old-reminder .reminder-description {
    color: #6c757d;
}

.old-reminder .delete-button {
    opacity: 1;
}

.old-reminder-badge {
    background-color: var(--neutral-light) !important;
    color: white !important;
}

.old-reminder-badge {
    background-color: #6c757d;
    color: white;
}

/* Needs attention styling */
.needs-attention-section {
    margin-top: 15px;
    margin-bottom: 30px;
    border-radius: 10px;
    background-color: rgba(220, 53, 69, 0.05);
    padding: 15px;
    border: 1px solid rgba(220, 53, 69, 0.2);
    color: var(--text-dark);
}

.all-reminders-section {
    margin-bottom: 20px;
}

/* Swipe hint styling */
.swipe-hint {
    display: flex;
    align-items: center;
    padding: 10px 12px;
    background-color: rgba(23, 162, 184, 0.1);
    color: var(--info);
    border-radius: 6px;
    font-size: 0.85rem;
    margin-bottom: 15px;
    position: relative;
}

.swipe-hint i.bi-arrow-left-right {
    font-size: 1rem;
    margin-right: 8px;
}

.dismiss-hint {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--info);
    padding: 4px;
    cursor: pointer;
    font-size: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.section-header {
    display: flex;
    align-items: center;
    font-size: 1.1rem;
    margin-bottom: 15px;
    color: var(--text-dark);
    padding-bottom: 8px;
    border-bottom: 1px solid var(--neutral-lighter);
}

.needs-attention-section .section-header {
    color: #dc3545;
}

.needs-attention-card {
    border-left: 4px solid #dc3545;
}

.attention-list {
    margin-bottom: 0;
}

.no-attention-needed {
    display: flex;
    align-items: center;
    padding: 18px;
    background-color: rgba(40, 167, 69, 0.1);
    color: var(--success);
    border-radius: 10px;
    font-weight: 500;
    margin-top: 15px;
    margin-bottom: 30px;
    box-shadow: 0 2px 8px var(--shadow-color);
    border: 1px solid rgba(40, 167, 69, 0.2);
}

.no-attention-needed i {
    font-size: 1.5rem;
    margin-right: 12px;
    color: #198754;
}

/* Additional mobile optimizations */
@media (max-width: 768px) {
    /* Optimize container padding for mobile */
    .container {
    }

    /* Make buttons more tappable on mobile */
    .btn, .delete-button {
        min-height: 44px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    /* Optimize reminder cards for mobile */
    .reminder-card {
        padding: 12px;
    }

    /* Ensure status badges wrap nicely */
    .reminder-status {
        max-width: 100%;
    }

    /* Make the footer stack on very small screens */
    @media (max-width: 380px) {
        .reminder-footer {
            flex-direction: column;
            align-items: flex-start;
        }

        .reminder-footer .delete-button {
            align-self: stretch;
            margin-top: 8px;
        }
    }
}
