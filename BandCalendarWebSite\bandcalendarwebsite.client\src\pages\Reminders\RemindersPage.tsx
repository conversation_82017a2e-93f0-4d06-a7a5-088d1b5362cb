import React, {useEffect, useState} from 'react';
import {format, parseISO} from 'date-fns';
import 'bootstrap-icons/font/bootstrap-icons.css';
import './RemindersPage.css';
import {useNetworkStatus} from '../../contexts/NetworkStatusContext';
import PullToRefreshWrapper from '../../components/PullToRefreshWrapper';
import BandCalendarApiService, {ReminderType} from '../../services/BandCalendarApiService';
import {ReminderCard} from "./ReminderCard";
import {Reminder} from "./types/Reminder";
import ReminderFilters from "./ReminderFilters";
import { useFilterRenderer } from '../../hooks';

interface RemindersPageProps {
    isMobile?: boolean;
}

const RemindersPage: React.FC<RemindersPageProps> = ({ isMobile = false }) => {
    const { isOnline } = useNetworkStatus();
    const [startDate, setStartDate] = useState(format(new Date(new Date().setMonth(new Date().getMonth() - 1)), 'yyyy-MM-dd'));
    const [endDate, setEndDate] = useState(format(new Date(new Date().setMonth(new Date().getMonth() + 1)), 'yyyy-MM-dd'));
    const [allReminders, setAllReminders] = useState<Reminder[]>([]);
    const [loading, setLoading] = useState(false);
    const [creating, setCreating] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [deleting, setDeleting] = useState<string | null>(null);
    const [deletingOutdated, setDeletingOutdated] = useState(false);
    const [synchronizing, setSynchronizing] = useState(false);
    const [syncCount, setSyncCount] = useState(0);
    const [showSwipeHint, setShowSwipeHint] = useState(() => {
        // Check localStorage to see if the user has dismissed the hint before
        const hintDismissed = localStorage.getItem('swipeHintDismissed');
        return hintDismissed !== 'true';
    });

    useEffect(() => {
        fetchAllReminders();
    }, []);

    // Save hint preference when it changes
    const dismissSwipeHint = () => {
        setShowSwipeHint(false);
        localStorage.setItem('swipeHintDismissed', 'true');
    };

    // Calculate the number of reminders that need synchronization
    useEffect(() => {
        if (allReminders.length === 0) {
            setSyncCount(0);
            return;
        }

        // Count reminders that need to be created (not created and not deleted)
        const toCreate = allReminders.filter(r =>
            !r.alreadyExists && !r.created && !r.deleted &&
            !r.isOutdated && !r.isOld
        ).length;

        // Count reminders that are outdated or old and need to be deleted
        const toDelete = allReminders.filter(r =>
            (r.isOutdated || r.isOld) && !r.deleted && r.reminderId
        ).length;

        setSyncCount(toCreate + toDelete);
    }, [allReminders]);

    const fetchAllReminders = async () => {
        setLoading(true);
        setError(null);

        try {
            // Use the unified method to get all reminders
            const reminders = await BandCalendarApiService.getReminders(startDate, endDate);

            // Format the reminders for display
            const formattedReminders = reminders.map((reminder: any) => {
                const date = reminder.startTime ? parseISO(reminder.startTime) : parseISO(reminder.date);

                // Validate the reminder type
                if (reminder.type !== ReminderType.ChargeEquipment && reminder.type !== ReminderType.DriveToGig) {
                    console.error(`Unknown reminder type: ${reminder.type}`);
                }

                // Format the end time if it exists
                let displayTime = '';
                if (reminder.startTime) {
                    const startDate = parseISO(reminder.startTime);
                    displayTime = format(startDate, 'EEE, MMM d, yyyy h:mm a');

                    // Add end time if it exists
                    if (reminder.endTime) {
                        const endDate = parseISO(reminder.endTime);
                        displayTime += ' - ' + format(endDate, 'h:mm a');
                    }
                } else {
                    displayTime = format(date, 'EEE, MMM d, yyyy');
                }

                return {
                    ...reminder,
                    displayTime,
                    sortDate: date
                };
            });

            // Sort by date
            const sorted = formattedReminders.sort((a, b) => {
                return a.sortDate.getTime() - b.sortDate.getTime();
            });

            setAllReminders(sorted);
        } catch (err) {
            console.error('Error fetching reminders:', err);
            setError('Failed to fetch reminders. Please try again later.');
        } finally {
            setLoading(false);
        }
    };

    const createReminders = async (type: string) => {
        setCreating(true);
        setError(null);

        try {
            // Map the UI type string to the ReminderType enum
            let reminderType: ReminderType | undefined;

            if (type === 'charge') {
                reminderType = ReminderType.ChargeEquipment;
            } else if (type === 'leave') {
                reminderType = ReminderType.DriveToGig;
            }
            // If type is 'all', leave reminderType undefined to create all types

            // Use the unified method to create reminders
            await BandCalendarApiService.createReminders(startDate, endDate, reminderType);
            return true; // Return true to indicate success
        } catch (err) {
            console.error('Error creating reminders:', err);
            setError('Failed to create reminders. Please try again later.');
            return false; // Return false to indicate failure
        } finally {
            setCreating(false);
        }
    };

    const deleteReminder = async (reminderId: string) => {
        if (!reminderId) return;

        setDeleting(reminderId);
        setError(null);

        try {
            const success = await BandCalendarApiService.deleteReminder(reminderId);

            if (success) {
                // Refresh the reminders list to get the updated state
                await fetchAllReminders();
            } else {
                setError('Failed to delete reminder. Please try again.');
            }
        } catch (err) {
            console.error('Error deleting reminder:', err);
            setError('Failed to delete reminder. Please try again later.');
        } finally {
            setDeleting(null);
        }
    };

    const deleteOutdatedReminders = async () => {
        setDeletingOutdated(true);
        setError(null);

        try {
            // Get all reminders that are either outdated or old and not already deleted
            const remindersToDelete = allReminders
                .filter(r => (r.isOutdated || r.isOld) && !r.deleted && r.reminderId)
                .map(r => r.reminderId as string);

            if (remindersToDelete.length === 0) {
                setError('No outdated or old reminders to delete.');
                setDeletingOutdated(false);
                return false; // Return false to indicate no reminders were deleted
            }

            const result = await BandCalendarApiService.deleteReminders(remindersToDelete);

            if (result.success) {
                // Mark all deleted reminders as deleted in the local state
                setAllReminders(prevReminders =>
                    prevReminders.map(reminder =>
                        remindersToDelete.includes(reminder.reminderId as string)
                            ? { ...reminder, deleted: true, alreadyExists: false, created: false }
                            : reminder
                    )
                );
                setError(null);
                return true; // Return true to indicate reminders were deleted
            } else {
                setError(result.message || 'Failed to delete some reminders. Please try again.');
                return false;
            }
        } catch (err) {
            console.error('Error deleting outdated reminders:', err);
            setError('Failed to delete outdated reminders. Please try again later.');
            return false;
        } finally {
            setDeletingOutdated(false);
        }
    };

    // Synchronize function that combines deleteOutdatedReminders and createReminders
    const synchronizeReminders = async () => {
        setSynchronizing(true);
        setError(null);

        try {
            // First, delete any outdated reminders
            await deleteOutdatedReminders();

            // Then create all reminders
            await createReminders('all');

            // Refresh the list to update UI
            await fetchAllReminders();
        } catch (err) {
            console.error('Error synchronizing reminders:', err);
            setError('Failed to synchronize reminders. Please try again later.');
        } finally {
            setSynchronizing(false);
        }
    };

    useFilterRenderer(
        () => (
            <ReminderFilters
                startDate={startDate}
                endDate={endDate}
                setStartDate={setStartDate}
                setEndDate={setEndDate}
                fetchAllReminders={fetchAllReminders}
                loading={loading}
            />
        ),
        [isMobile, startDate, endDate, loading]
    );

    return (
        <div className="container mx-auto">
            <PullToRefreshWrapper onRefresh={fetchAllReminders} isPullable={isMobile}>
                {loading && <div className="status-message loading"><i className="bi bi-hourglass-split me-2"></i>Loading reminders...</div>}
                {creating && <div className="status-message creating"><i className="bi bi-arrow-clockwise me-2"></i>Creating reminders...</div>}
                {synchronizing && <div className="status-message creating"><i className="bi bi-arrow-repeat me-2"></i>Synchronizing reminders...</div>}
                {error && <div className="status-message error"><i className="bi bi-exclamation-triangle-fill me-2"></i>{error}</div>}

            {!loading && !creating && !synchronizing && !error && (
                allReminders.length > 0 ? (
                    <>
                        {/* Conditional Section - Either Needs Attention or All Up To Date */}
                        {syncCount > 0 ? (
                            <div className="needs-attention-section">
                                <h3 className="section-header">
                                    <i className="bi bi-exclamation-circle-fill me-2"></i>
                                    Needs Attention ({syncCount})
                                </h3>

                                <button
                                    onClick={synchronizeReminders}
                                    disabled={creating || deletingOutdated || !isOnline || synchronizing}
                                    className={`btn btn-accent mb-3 w-100 ${!isOnline ? 'offline-disabled' : ''}`}
                                    title={isOnline
                                        ? "Synchronize reminders (delete outdated and create new)"
                                        : "Cannot synchronize reminders while offline"}
                                >
                                    <i className="bi bi-arrow-repeat me-1"></i>
                                    {synchronizing
                                        ? 'Synchronizing...'
                                        : 'Synchronize'}
                                </button>

                                <div className="reminders-list attention-list">
                                    {allReminders
                                        .filter(r => !r.deleted &&
                                            (r.isOutdated || (!r.alreadyExists && !r.created && !r.isOld)))
                                        .map((reminder, index) => (
                                            <ReminderCard
                                                key={`attention-${index}`}
                                                reminder={reminder}
                                                deleteReminder={deleteReminder}
                                                deleting={deleting}
                                                isOnline={isOnline}
                                            />
                                        ))
                                    }
                                </div>
                            </div>
                        ) : (
                            <div className="no-attention-needed">
                                <i className="bi bi-check-circle-fill me-2"></i>
                                All reminders are up to date. No action needed.
                            </div>
                        )}

                        {/* All reminders in chronological order */}
                        <div className="all-reminders-section">
                            <h3 className="section-header">
                                <i className="bi bi-calendar-date me-2"></i>
                                All Reminders ({allReminders.filter(r => !r.deleted).length})
                            </h3>

                            {showSwipeHint && window.innerWidth < 768 && (
                                <div className="swipe-hint">
                                    <i className="bi bi-arrow-left-right me-2"></i>
                                    Swipe left on a reminder to reveal the delete button
                                    <button
                                        className="dismiss-hint"
                                        onClick={dismissSwipeHint}
                                        aria-label="Dismiss hint"
                                    >
                                        <i className="bi bi-x"></i>
                                    </button>
                                </div>
                            )}

                            <div className="reminders-list">
                                {allReminders.filter(r => !r.deleted).map((reminder, index) => (
                                    <ReminderCard
                                        key={`all-${index}`}
                                        reminder={reminder}
                                        deleteReminder={deleteReminder}
                                        deleting={deleting}
                                        isOnline={isOnline}
                                    />
                                ))}
                            </div>
                        </div>
                    </>
                ) : (
                    <p>No reminders found for the selected date range.</p>
                )
            )}
            </PullToRefreshWrapper>
        </div>
    );
};

export default RemindersPage;
