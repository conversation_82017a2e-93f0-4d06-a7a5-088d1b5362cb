export interface Reminder {
    date: string;
    startTime?: string; // Renamed from time for clarity
    endTime?: string; // Added for direct end time setting
    title: string;
    description: string;
    type: string;
    displayTime: string;
    sortDate: Date;
    alreadyExists: boolean;
    created: boolean;
    isOutdated?: boolean;
    isOld?: boolean; // Added to identify reminders with dates in the past
    outdatedReason?: string; // Reason why the reminder is outdated
    relatedGigId?: string;
    reminderId?: string;
    deleted?: boolean; // Flag to indicate if the reminder has been deleted locally
}