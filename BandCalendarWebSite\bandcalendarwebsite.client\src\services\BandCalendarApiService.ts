import axios from 'axios';
import {ApiError} from "../api/axiosConfig.ts";

const API_BASE_URL = '/api';

// Add a flag to track if we're in offline mode
let isOfflineMode = false;

// Define interfaces for the data returned by each API call
export interface CalendarEvent {
    id: string;
    summary: string;
    description: string;
    location: string;
    startTime: string;
    endTime: string;
    isAllDay: boolean;
    mapLink?: string;
    htmlLink?: string;
    isAvailabilityEvent: boolean;
    unavailableMembers?: UnavailabilityInfo[];
}

export interface UnavailabilityInfo {
    memberName: string;
    availabilityEventId: string;
}

export enum ReminderType {
    ChargeEquipment = 'ChargeEquipment',
    DriveToGig = 'DriveToGig'
}

export interface ReminderInfo {
    date: string;
    startTime?: string; // Renamed from time for clarity
    endTime?: string; // Added for direct end time setting
    title: string;
    description: string;
    location?: string;
    alreadyExists: boolean;
    created: boolean;
    relatedGigId?: string;
    isOutdated?: boolean;
    isOld?: boolean; // Added to identify reminders with dates in the past
    outdatedReason?: string; // Reason why the reminder is outdated
    reminderId?: string;
    type?: ReminderType; // Added to identify the type of reminder
}

export interface VenueBreakdown {
    [venue: string]: number;
}

// Listen for offline events
if (typeof window !== 'undefined') {
    window.addEventListener('offline', () => {
        isOfflineMode = true;

    });

    window.addEventListener('online', () => {
        isOfflineMode = false;

    });

    window.addEventListener('app-offline', () => {
        isOfflineMode = true;

    });
}

const BandCalendarApiService = {
    // Check if we're in offline mode
    isOffline: (): boolean => {
        return isOfflineMode || !navigator.onLine;
    },

    // Configuration
    getMapsApiKey: async (): Promise<string> => {
        try {
            const response = await axios.get(`${API_BASE_URL}/config/maps-api-key`);
            return response.data;
        } catch (error: unknown) {
            const apiError = error as ApiError;
            if (apiError?.isOffline) {
                return 'OFFLINE_MAPS_API_KEY';
            }
            throw error;
        }
    },

    // Events
    getGigEvents: async (
        startDate: string,
        endDate: string,
        location: string | null = null
    ): Promise<CalendarEvent[]> => {
        try {
            const response = await axios.get(`${API_BASE_URL}/gigs`, {
                params: {
                    startDate,
                    endDate,
                    location: location || undefined // Only include if it has a value
                }
            });

            return response.data;
        } catch (error: unknown) {
            const apiError = error as ApiError;
            if (apiError?.isOffline) {
                return []; // Return empty array when offline
            }
            throw error;
        }
    },

    getLocations: async (): Promise<string[]> => {
        try {
            const response = await axios.get(`${API_BASE_URL}/gigs/locations`);

            return response.data;
        } catch (error: unknown) {
            const apiError = error as ApiError;
            if (apiError?.isOffline) {
                return []; // Return empty array when offline
            }
            throw error;
        }
    },

    // New unified methods
    getReminders: async (
        startDate: string,
        endDate: string,
        type?: ReminderType
    ): Promise<ReminderInfo[]> => {
        try {
            const response = await axios.get(`${API_BASE_URL}/reminders`, {
                params: { startDate, endDate, type }
            });

            return response.data;
        } catch (error: unknown) {
            const apiError = error as ApiError;
            if (apiError?.isOffline) {
                return []; // Return empty array when offline
            }
            throw error;
        }
    },

    createReminders: async (
        startDate: string,
        endDate: string,
        type?: ReminderType
    ): Promise<ReminderInfo[]> => {
        // Check if we're offline before attempting to create
        if (BandCalendarApiService.isOffline()) {
            throw new Error('Cannot create reminders while offline');
        }

        try {
            const response = await axios.post(`${API_BASE_URL}/reminders`, {
                startDate,
                endDate,
                type
            });
            return response.data;
        } catch (error: unknown) {
            const apiError = error as ApiError;
            if (apiError?.isOffline) {
                throw new Error('Cannot create reminders while offline');
            }
            throw error;
        }
    },

    deleteReminder: async (reminderId: string): Promise<boolean> => {
        // Check if we're offline before attempting to delete
        if (BandCalendarApiService.isOffline()) {
            throw new Error('Cannot delete reminders while offline');
        }

        try {
            const response = await axios.delete(`${API_BASE_URL}/reminders/${reminderId}`);
            return response.data.success;
        } catch (error: unknown) {
            const apiError = error as ApiError;
            if (apiError?.isOffline) {
                throw new Error('Cannot delete reminders while offline');
            }
            console.error('Error deleting reminder:', error);
            return false;
        }
    },

    deleteReminders: async (reminderIds: string[]): Promise<{success: boolean, message: string}> => {
        // Check if we're offline before attempting to delete
        if (BandCalendarApiService.isOffline()) {
            return {
                success: false,
                message: 'Cannot delete reminders while offline'
            };
        }

        try {
            const response = await axios.post(`${API_BASE_URL}/reminders/delete`, {
                reminderIds
            });
            return {
                success: response.data.success,
                message: response.data.message
            };
        } catch (error: unknown) {
            const apiError = error as ApiError;
            if (apiError?.isOffline) {
                return {
                    success: false,
                    message: 'Cannot delete reminders while offline'
                };
            }
            console.error('Error deleting multiple reminders:', error);
            return {
                success: false,
                message: 'Failed to delete reminders. Please try again later.'
            };
        }
    }
};

export default BandCalendarApiService;
