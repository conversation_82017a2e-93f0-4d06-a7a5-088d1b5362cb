// No need to import useGoogleMaps as we'll check for Google Maps API availability directly

// Define the interface for geocoding results
export interface GeoCoordinates {
  lat: number;
  lng: number;
}

// Define the interface for cached geocoding results
interface GeoCacheEntry {
  timestamp: number;
  data: GeoCoordinates;
}

/**
 * Service for geocoding addresses using Google Maps Geocoding API
 */
class GeocodingService {
  // Cache for geocoding results to minimize API calls
  private geoCache: Record<string, GeoCacheEntry> = {};

  // Cache duration in milliseconds (24 hours)
  private geoCacheDuration: number = 24 * 60 * 60 * 1000;

  /**
   * Geocode an address string to coordinates using Google Maps Geocoding API
   * @param address The address to geocode
   * @returns Promise with latitude and longitude
   */
  async geocodeAddress(address: string): Promise<GeoCoordinates | null> {
    if (!address || address.trim() === '') {
      console.warn('GeocodingService: Empty address provided');
      return null;
    }

    try {
      // Create a cache key based on the address string
      const cacheKey = address.toLowerCase().trim();

      // Check if we have a valid cached response
      const now = Date.now();
      if (this.geoCache[cacheKey] && (now - this.geoCache[cacheKey].timestamp) < this.geoCacheDuration) {

        return this.geoCache[cacheKey].data;
      }

      // Check if Google Maps API is loaded
      if (typeof google === 'undefined' || !google.maps) {
        console.error('GeocodingService: Google Maps API not loaded');

        // Wait for Google Maps API to load (retry up to 3 times with a 1-second delay)
        let retries = 0;
        while (retries < 3) {
          await new Promise(resolve => setTimeout(resolve, 1000));
          if (typeof google !== 'undefined' && google.maps) {

            break;
          }
          retries++;
        }

        // If still not loaded after retries, return null
        if (typeof google === 'undefined' || !google.maps) {
          console.error('GeocodingService: Google Maps API still not loaded after retries');
          return null;
        }
      }

      // Use Google Maps Geocoding API
      const geocoder = new google.maps.Geocoder();

      return new Promise<GeoCoordinates | null>((resolve) => {
        geocoder.geocode({ address }, (results, status) => {
          if (status === google.maps.GeocoderStatus.OK && results && results[0]) {
            const location = results[0].geometry.location;

            const coordinates: GeoCoordinates = {
              lat: location.lat(),
              lng: location.lng()
            };

            // Cache the result
            this.geoCache[cacheKey] = {
              timestamp: now,
              data: coordinates
            };


            resolve(coordinates);
          } else {
            console.warn(`GeocodingService: Geocoding failed for address: ${address}. Status: ${status}`);
            resolve(null);
          }
        });
      });
    } catch (error) {
      console.error('GeocodingService: Error geocoding address:', error);
      return null;
    }
  }

  /**
   * Clear the geocoding cache
   */
  clearCache(): void {

    this.geoCache = {};
  }
}

// Create a singleton instance
const geocodingService = new GeocodingService();

export default geocodingService;
