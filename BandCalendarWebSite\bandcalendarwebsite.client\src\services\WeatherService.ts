// Weather Service for fetching weather forecasts from Open-Meteo API
import geocodingService from './GeocodingService';

export interface WeatherForecast {
    time: string;
    weatherCode: number;
    temperature: number;
    precipitation: number;
    precipitationProbability: number;
    windSpeed: number;
    weatherDescription: string;
    weatherIcon: string;
}

export interface WeatherResponse {
    latitude: number;
    longitude: number;
    generationtime_ms: number;
    utc_offset_seconds: number;
    timezone: string;
    timezone_abbreviation: string;
    elevation: number;
    hourly_units: {
        time: string;
        temperature_2m: string;
        precipitation: string;
        precipitation_probability: string;
        weathercode: string;
        windspeed_10m: string;
    };
    hourly: {
        time: string[];
        temperature_2m: number[];
        precipitation: number[];
        precipitation_probability: number[];
        weathercode: number[];
        windspeed_10m: number[];
    };
}

// Weather code mapping based on WMO codes used by Open-Meteo
// https://open-meteo.com/en/docs
export const weatherCodeMap: Record<number, { description: string, icon: string }> = {
    0: { description: "Clear sky", icon: "☀️" },
    1: { description: "Mainly clear", icon: "🌤️" },
    2: { description: "Partly cloudy", icon: "⛅" },
    3: { description: "Overcast", icon: "☁️" },
    45: { description: "Fog", icon: "🌫️" },
    48: { description: "Depositing rime fog", icon: "🌫️" },
    51: { description: "Light drizzle", icon: "🌦️" },
    53: { description: "Moderate drizzle", icon: "🌦️" },
    55: { description: "Dense drizzle", icon: "🌧️" },
    56: { description: "Light freezing drizzle", icon: "🌨️" },
    57: { description: "Dense freezing drizzle", icon: "🌨️" },
    61: { description: "Slight rain", icon: "🌦️" },
    63: { description: "Moderate rain", icon: "🌧️" },
    65: { description: "Heavy rain", icon: "🌧️" },
    66: { description: "Light freezing rain", icon: "🌨️" },
    67: { description: "Heavy freezing rain", icon: "🌨️" },
    71: { description: "Slight snow fall", icon: "🌨️" },
    73: { description: "Moderate snow fall", icon: "🌨️" },
    75: { description: "Heavy snow fall", icon: "❄️" },
    77: { description: "Snow grains", icon: "❄️" },
    80: { description: "Slight rain showers", icon: "🌦️" },
    81: { description: "Moderate rain showers", icon: "🌧️" },
    82: { description: "Violent rain showers", icon: "🌧️" },
    85: { description: "Slight snow showers", icon: "🌨️" },
    86: { description: "Heavy snow showers", icon: "❄️" },
    95: { description: "Thunderstorm", icon: "⛈️" },
    96: { description: "Thunderstorm with slight hail", icon: "⛈️" },
    99: { description: "Thunderstorm with heavy hail", icon: "⛈️" }
};

// Function to get the most severe weather code from a range of hours
export const getMostSevereWeather = (weatherCodes: number[]): number => {
    if (!weatherCodes || weatherCodes.length === 0) return 0;

    // Priority order: thunderstorm > heavy precipitation > moderate precipitation > light precipitation > overcast > partly cloudy > clear
    const severityOrder = [
        [95, 96, 99], // Thunderstorms
        [65, 67, 75, 77, 82, 86], // Heavy precipitation
        [63, 73, 81, 85], // Moderate precipitation
        [51, 53, 55, 56, 57, 61, 66, 71, 80], // Light precipitation
        [45, 48], // Fog
        [3], // Overcast
        [2], // Partly cloudy
        [1], // Mainly clear
        [0]  // Clear sky
    ];

    // Find the most severe weather code
    for (const severityGroup of severityOrder) {
        for (const code of weatherCodes) {
            if (severityGroup.includes(code)) {
                return code;
            }
        }
    }

    // If no match found, return the first code
    return weatherCodes[0];
};

interface WeatherForecastCacheEntry {
    timestamp: number;
    data: WeatherForecast[];
}

class WeatherService {
    private baseUrl = 'https://api.open-meteo.com/v1/forecast';
    private forecastCache: Record<string, WeatherForecastCacheEntry> = {};
    private forecastCacheDuration = 30 * 60 * 1000; // 30 minutes in milliseconds

    /**
     * Get weather forecast for a specific location and time range
     * @param latitude Location latitude
     * @param longitude Location longitude
     * @param startDate Start date in ISO format (YYYY-MM-DD)
     * @param endDate End date in ISO format (YYYY-MM-DD)
     * @returns Promise with weather forecast data
     */
    async getWeatherForecast(
        latitude: number,
        longitude: number,
        startDate: string,
        endDate: string
    ): Promise<WeatherForecast[]> {
        try {
            // Create a cache key based on the parameters
            const cacheKey = `${latitude.toFixed(4)}_${longitude.toFixed(4)}_${startDate}_${endDate}`;

            // Check if we have a valid cached response
            const now = Date.now();
            if (this.forecastCache[cacheKey] && (now - this.forecastCache[cacheKey].timestamp) < this.forecastCacheDuration) {

                return this.forecastCache[cacheKey].data;
            }

            // Format the URL with query parameters
            // For the Open-Meteo API, the end_date is inclusive, meaning it will include
            // all hours of that day up to 23:00. This is important for events spanning midnight.
            const url = `${this.baseUrl}?latitude=${latitude}&longitude=${longitude}&hourly=temperature_2m,precipitation,precipitation_probability,weathercode,windspeed_10m&timezone=America%2FNew_York&wind_speed_unit=mph&temperature_unit=fahrenheit&precipitation_unit=inch&start_date=${startDate}&end_date=${endDate}`;

            const response = await fetch(url);
            if (!response.ok) {
                throw new Error(`Weather API error: ${response.status}`);
            }

            const data: WeatherResponse = await response.json();


            // Process the hourly data into a more usable format
            const forecasts: WeatherForecast[] = [];

            for (let i = 0; i < data.hourly.time.length; i++) {
                const time = data.hourly.time[i];
                const weatherCode = data.hourly.weathercode[i];
                const weatherInfo = weatherCodeMap[weatherCode] || {
                    description: "Unknown",
                    icon: "❓"
                };

                forecasts.push({
                    time,
                    weatherCode,
                    temperature: data.hourly.temperature_2m[i],
                    precipitation: data.hourly.precipitation[i],
                    precipitationProbability: data.hourly.precipitation_probability[i],
                    windSpeed: data.hourly.windspeed_10m[i],
                    weatherDescription: weatherInfo.description,
                    weatherIcon: weatherInfo.icon
                });
            }

            // Cache the result
            this.forecastCache[cacheKey] = {
                timestamp: now,
                data: forecasts
            };

            return forecasts;
        } catch (error) {
            console.error('Error fetching weather data:', error);
            return [];
        }
    }
    /**
     * Clear forecast cache
     */
    clearCache(): void {

        this.forecastCache = {};
        // Note: We no longer need to clear the geoCache as we're using the shared geocoding service
    }

    /**
     * Get weather forecast for an event
     * @param location Event location
     * @param startTime Event start time
     * @param endTime Event end time
     * @returns Promise with the most severe weather during the event
     */
    async getEventWeather(location: string, startTime: string, endTime: string): Promise<WeatherForecast | null> {
        try {
            // Check if event is within the next 15 days
            const now = new Date();
            const fifteenDaysFromNow = new Date(now);
            fifteenDaysFromNow.setDate(now.getDate() + 15);

            const eventStartDate = new Date(startTime);
            if (eventStartDate > fifteenDaysFromNow) {
                console.log("Skipping event more than 15 days out")
                return null;
            }

            const coordinates = await geocodingService.geocodeAddress(location);
            if (!coordinates) {
                console.warn(`Could not find coordinates for location: ${location}`);
                return null;
            }

            // Format dates for API call
            const startDate = startTime.split('T')[0];
            const endDate = endTime.split('T')[0];

            // Get weather forecast for the date range
            const forecasts = await this.getWeatherForecast(
                coordinates.lat,
                coordinates.lng,
                startDate,
                endDate
            );

            if (forecasts.length === 0) {
                return null;
            }

            // Filter forecasts to only include those during the event
            const eventStart = new Date(startTime).getTime();
            const eventEnd = new Date(endTime).getTime();

            const eventForecasts = forecasts.filter(forecast => {
                const forecastTime = new Date(forecast.time).getTime();
                return forecastTime >= eventStart && forecastTime <= eventEnd;
            });

            if (eventForecasts.length === 0) {
                // If no forecasts during the event time, return the closest one
                const closestForecast = forecasts.reduce((prev, curr) => {
                    const prevTime = Math.abs(new Date(prev.time).getTime() - eventStart);
                    const currTime = Math.abs(new Date(curr.time).getTime() - eventStart);
                    return prevTime < currTime ? prev : curr;
                });

                return closestForecast;
            }

            // Get the most severe weather during the event
            const weatherCodes = eventForecasts.map(f => f.weatherCode);
            const mostSevereCode = getMostSevereWeather(weatherCodes);

            // Find the forecast with this code
            const severeForecast = eventForecasts.find(f => f.weatherCode === mostSevereCode) || eventForecasts[0];

            return severeForecast;
        } catch (error) {
            console.error('Error getting event weather:', error);
            return null;
        }
    }
}

export default new WeatherService();
