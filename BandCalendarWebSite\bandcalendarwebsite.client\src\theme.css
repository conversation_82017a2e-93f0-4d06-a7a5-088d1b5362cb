/* Theme CSS with light and dark mode support */

:root {
  /* Primary colors - Light Mode */
  --primary-bg: #4a95b0; /* Blue-teal background - adjusted to be more blue and darker */
  --primary-darker: #3a7a90; /* Darker shade for hover states */
  --primary-lighter: #5baac5; /* Lighter shade for highlights */

  /* Accent colors - Light Mode */
  --accent: #e05e35; /* Orange/red accent */
  --accent-darker: #c04d28; /* Darker shade for hover states */
  --accent-lighter: #f06e45; /* Lighter shade for highlights */

  /* Neutral colors - Light Mode */
  --neutral-dark: #333333;
  --neutral-medium: #666666;
  --neutral-light: #999999;
  --neutral-lighter: #eeeeee;

  /* Background colors - Light Mode */
  --bg-main: #ffffff;
  --bg-secondary: rgba(74, 149, 176, 0.15); /* Lighter version of primary blue */
  --bg-tertiary: rgba(74, 149, 176, 0.25); /* Slightly darker version of bg-secondary */

  /* Text colors - Light Mode */
  --text-dark: #333333;
  --text-medium: #555555;
  --text-light: #777777;
  --text-on-primary: #ffffff;
  --text-on-accent: #ffffff;

  /* Status colors - Light Mode */
  --success: #28a745;
  --warning: #ffc107;
  --danger: #dc3545;
  --info: #17a2b8;

  /* Specific component colors - Light Mode */
  --navbar-bg: var(--primary-bg);
  --navbar-text: var(--text-on-primary);
  --card-bg: var(--bg-main);
  --card-border: var(--primary-lighter);
  --run-container-bg: var(--bg-secondary);
  --toggle-bg: var(--accent);
  --warning-bg: #fff3cd;
  --warning-text: #856404;
  --past-event-bg: #f8f9fa;
  --past-event-text: #6c757d;
  --dropdown-bg: #ffffff;
  --dropdown-text: #333333;
  --dropdown-hover: #f8f9fa;
  --dropdown-border: #dee2e6;
  --backdrop-color: rgba(0, 0, 0, 0.3);
  --shadow-color: rgba(0, 0, 0, 0.1);
}

/* Dark Mode Theme */
[data-theme="dark"] {
  /* Primary colors - Dark Mode */
  --primary-bg: #2c5a7a; /* Dark blue for navbar */
  --primary-darker: #1e3e55; /* Darker shade for hover states */
  --primary-lighter: #3a7095; /* Slightly lighter shade for highlights */

  /* Accent colors - Dark Mode */
  --accent: #e05e35; /* Keep the same orange/red accent for consistency */
  --accent-darker: #c04d28; /* Slightly darker for better contrast */
  --accent-lighter: #f06e45; /* Keep the same lighter accent */

  /* Neutral colors - Dark Mode */
  --neutral-dark: #e6e6e6;
  --neutral-medium: #b0b0b0;
  --neutral-light: #808080;
  --neutral-lighter: #2c4a65;

  /* Background colors - Dark Mode */
  --bg-main: #162c3d; /* Dark blue for main background */
  --bg-secondary: #1e3a50; /* Slightly lighter blue for secondary elements */
  --bg-tertiary: #254863; /* Even lighter blue for tertiary elements */

  /* Text colors - Dark Mode */
  --text-dark: #e6e6e6; /* Light gray for main text */
  --text-medium: #b0b0b0; /* Medium gray for secondary text */
  --text-light: #808080; /* Darker gray for tertiary text */
  --text-on-primary: #ffffff;
  --text-on-accent: #ffffff;

  /* Status colors - Dark Mode */
  --success: #4cd964;
  --warning: #ffcc00;
  --danger: #ff3b30;
  --info: #5ac8fa;

  /* Specific component colors - Dark Mode */
  --navbar-bg: var(--primary-bg);
  --navbar-text: var(--text-on-primary);
  --card-bg: #1e3a50; /* Card background - blue tinted */
  --card-border: #2c4a65;
  --run-container-bg: #1a3448; /* Run container background - blue tinted */
  --toggle-bg: var(--accent);
  --warning-bg: #3a3000;
  --warning-text: #ffcc00;
  --past-event-bg: #1a3448;
  --past-event-text: #a0a0a0;
  --dropdown-bg: #1e3a50;
  --dropdown-text: #e6e6e6;
  --dropdown-hover: #254863;
  --dropdown-border: #2c4a65;
  --backdrop-color: rgba(0, 0, 0, 0.5);
  --shadow-color: rgba(13, 25, 36, 0.3);
}

/* Apply theme to common elements */
body {
  background-color: var(--bg-main);
  color: var(--text-dark);
}

a {
  color: var(--accent);
}

a:hover {
  color: var(--accent-darker);
}

button {
  background-color: var(--primary-bg);
  color: var(--text-on-primary);
}

button:hover {
  background-color: var(--primary-darker);
  border-color: var(--primary-darker);
}

.btn-primary {
  background-color: var(--primary-bg);
  border-color: var(--primary-bg);
  color: var(--text-on-primary);
}

.btn-primary:hover {
  background-color: var(--primary-darker);
  border-color: var(--primary-darker);
}

.btn-accent {
  background-color: var(--accent);
  border-color: var(--accent);
  color: var(--text-on-accent);
}

.btn-accent:hover {
  background-color: var(--accent-darker);
  border-color: var(--accent-darker);
}

/* Navbar styling */
.top-navbar, .bottom-navbar-container, .navbar {
  background-color: var(--navbar-bg);
  color: var(--navbar-text);
}

.bottom-navbar .nav-item.active {
  background-color: rgba(255, 255, 255, 0.2);
}

/* Event styling */
.run-container {
  background-color: var(--run-container-bg);
}

.gig-item {
  border-left: 4px solid var(--accent);
}

.calendar-icon {
  color: var(--accent);
}

.month-count {
  background-color: var(--accent);
}

.month-header h2 {
  color: var(--primary-bg);
}

/* Toggle switch styling */
.toggle-switch {
  background-color: var(--neutral-light);
}

.pill-checkbox-label input:checked + .toggle-switch {
  background-color: var(--accent);
}

/* Warning box styling */
.warning-box {
  background-color: var(--warning-bg);
  color: var(--warning-text);
  border-left: 3px solid var(--warning);
}

/* Filter menu styling */
.filter-menu {
  background-color: var(--bg-main);
}

.filter-menu-header {
  border-bottom: 1px solid var(--bg-secondary);
}

/* Offline indicator */
.offline-indicator.offline {
  background-color: var(--danger);
  color: var(--text-on-accent);
}

.offline-indicator.cached-data {
  background-color: var(--warning);
  color: var(--text-dark);
}

/* Analytics bars */
.bar {
  background-color: var(--accent);
}

.bar-container {
  background-color: var(--bg-secondary);
}

/* Calendar-specific CSS variables */
:root {
  /* Calendar light mode colors */
  --card-bg-color: var(--bg-main);
  --header-bg-color: var(--bg-secondary);
  --button-bg-color: var(--bg-main);
  --button-hover-bg-color: var(--bg-secondary);
  --border-color: var(--primary-lighter);
  --text-color: var(--text-dark);
  --text-muted-color: var(--text-light);
  --secondary-bg-color: var(--bg-secondary);
  --hover-bg-color: var(--bg-secondary);
  --muted-bg-color: var(--neutral-lighter);
  --today-bg-color: rgba(74, 149, 176, 0.2);
  --gig-event-bg-color: var(--primary-bg);
  --gig-event-text-color: var(--text-on-primary);
  --availability-event-bg-color: var(--accent);
  --availability-event-text-color: var(--text-on-accent);
  --input-bg-color: var(--bg-main);
  --primary-color: var(--primary-bg);
  --primary-hover-color: var(--primary-darker);
  --warning-color: var(--warning);
  --error-color: var(--danger);
  --error-bg-color: rgba(220, 53, 69, 0.1);
}

/* Calendar dark mode colors */
[data-theme="dark"] {
  --card-bg-color: #2d3748;
  --header-bg-color: #1a202c;
  --button-bg-color: #4a5568;
  --button-hover-bg-color: #5a6578;
  --border-color: #4a5568;
  --text-color: #f7fafc;
  --text-muted-color: #a0aec0;
  --secondary-bg-color: #2d3748;
  --hover-bg-color: #4a5568;
  --muted-bg-color: #1a202c;
  --today-bg-color: rgba(66, 153, 225, 0.2);
  --gig-event-bg-color: rgba(66, 153, 225, 0.3);
  --gig-event-text-color: #90cdf4;
  --availability-event-bg-color: rgba(245, 101, 101, 0.3);
  --availability-event-text-color: #feb2b2;
  --input-bg-color: #2d3748;
  --primary-color: #4299e1;
  --primary-hover-color: #3182ce;
  --warning-color: #f56565;
  --error-color: #f56565;
  --error-bg-color: rgba(245, 101, 101, 0.1);
}
