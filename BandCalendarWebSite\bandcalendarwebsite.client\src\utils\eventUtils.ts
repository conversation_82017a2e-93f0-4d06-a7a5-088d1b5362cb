import {CalendarEvent as Event, CalendarEvent} from '../services/BandCalendarApiService';

/**
 * Checks if an event is in the past
 * @param event The event to check
 * @returns true if the event end time is in the past
 */
export const isEventInPast = (event: CalendarEvent): boolean => {
    const end = new Date(event.endTime);
    const now = new Date();
    return end < now;
};

/**
 * Checks if an event summary matches the expected format
 * @param summary The event summary to check
 * @returns true if the summary does NOT match the expected format (Gig prefix followed by - or space and additional words)
 */
export const eventHasInvalidSummary = (summary: string): boolean => {
    // The regex checks for "Gig" followed by some combination of '-' and ' ' chars, followed by additional words
    const regex = /^Gig[\s-]+\w+/;
    return !regex.test(summary);
};

export const eventHasWarnings = (event: Event)=> {
    // We currently don't check for any warnings on availability events
    if (event.isAvailabilityEvent)
        return false;

    // Check for missing location
    const missingLocation = !event.location || event.location.trim() === '';

    // Check for all-day event
    const startTime = new Date(event.startTime);
    const endTime = new Date(event.endTime);
    const isAllDay = event.isAllDay ||
        (startTime.getHours() === 0 &&
            startTime.getMinutes() === 0 &&
            endTime.getHours() === 0 &&
            endTime.getMinutes() === 0);

    // Check for invalid summary format
    const invalidSummaryFormat = eventHasInvalidSummary(event.summary);

    // Return true if event has any warnings
    return missingLocation || isAllDay || invalidSummaryFormat;
}
