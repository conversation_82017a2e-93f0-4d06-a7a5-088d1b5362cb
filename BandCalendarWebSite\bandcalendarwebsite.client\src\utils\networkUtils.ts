/**
 * Utility functions for network status and API requests
 */

/**
 * Checks if the browser is online and can make API requests
 * @returns Promise that resolves to true if online, false if offline
 */
export const checkOnlineStatus = async (): Promise<boolean> => {
  // First check the browser's navigator.onLine property
  if (!navigator.onLine) {
    return false;
  }

  // Then try to fetch a small resource to confirm connectivity
  try {
    // Try to fetch the favicon or another small resource
    const response = await fetch('/nannies_120x120.png', {
      method: 'HEAD',
      cache: 'no-cache',
      // Set a short timeout to avoid long waits
      signal: AbortSignal.timeout(2000)
    });

    return response.ok;
  } catch {
    return false;
  }
};

/**
 * Checks if the service worker is active and controlling the page
 * @returns True if service worker is controlling the page
 */
export const isServiceWorkerActive = (): boolean => {
  return !!navigator.serviceWorker && !!navigator.serviceWorker.controller;
};

/**
 * Sends a message to the service worker
 * @param message The message to send
 * @returns Promise that resolves when the message is sent
 */
export const sendServiceWorkerMessage = async (message: any): Promise<void> => {
  if (!isServiceWorkerActive()) {
    console.warn('No active service worker to send message to');
    return;
  }

  navigator.serviceWorker.controller!.postMessage(message);
};

/**
 * Clears the API cache by sending a message to the service worker
 * @returns Promise that resolves when the cache is cleared
 */
export const clearApiCache = async (): Promise<void> => {
  return sendServiceWorkerMessage({ type: 'CLEAR_API_CACHE' });
};
