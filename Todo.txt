

Thoughts on multi-gig days
=================================================================
Params:
- Gig1EndTime
- Gig2StartTime
- DriveTimeToGig2FromGig1
- DriveTimeToHomeFromGig1
- DriveTimeToGig2FromHome
- MinBreakdownTimeSpan
- MinSetupTimeSpan
- DesiredSetupTimeSpan
- DesiredSetupTime
- LatestSetupTime

Scenarios:
1. Two gigs are <= drive time plus arrival buffer away (breakdown time + desired setup time). 
	a. If arrival time <= min setup time, create drive reminder with alert emoji/messaging.
	b. If arrival time > min setup time, create drive reminder from gig1 to gig2. 
2. Two gigs are >= drive time plus arrival buffer away.
	a. If enough time to go home?
		- ?
	b. If not enough time to go home, treat same as 1.b (create drive reminder from gig1 to gig2).





5/13/2025 Ideas
--------------------
- Dark mode (add to hamburger menu)
- Offline mode (add to hamburger menu)
- Share events

----------------------
Misc:
- Is there a way to just wrap Events and Reminders into one thing? Or is that too busy? 
	- If not, maybe Reminders should have better visible connections to the specific events?
- Events and Reminders should support pulldown to refresh.
- Refresh tokens for Google
	- This seems easy to enable but is it secure if stored in the cookie? Wouldn't any theft of the cookie give perpetual access to calendars?

Events Page:
x- Gigs that are in the past should be grayed out to indicate this. 
- Gig time in the event list should link to Google Calendar event (like gig title).
- Each individual gig should have an icon or hamburger menu that is right aligned on the same line as the event icon/time. 
	- This should have the following commands:
		- "Filter to location" that sets the Location dropdown to the gig location. 
		- "Drive from home" that opens default maps application with directions from home to the gig location.
		- "Detailed weather" that links to weather site for gig location zip code and date/time.
- Add a warning for non "Gig-" formatted events.
x- Add a toggle to the filters to show past events. When true, past events should shown (as is currently the state). When false, past events should not be included in the visible list. The default should be false. 

Reminders:
- Visually highlight reminder state better i.e. exists vs. doesn't exist vs. outdated.
- Deleted reminders should just go away rather than being tagged as "Deleted" and still showing.
	- This should already work like this. Was I just running some old code or something? 
- Tighter vertical layout. 
- Drive reminders should be more intelligent when there are multiple gigs without time to get home.
x- BUG: Reminders shouldn't be created for past events

- Change Delete button into a simple X that is right aligned across from the reminder title.
- For non-created reminders, put "Not Created" pill here. 
- Use dynamic ellipse for truncated reminder titles that would otherwise wrap.
- Remove "Exists" pill from created reminders.

Azure:
- Figure out cold start issue.
- Configure Azure monitoring
	- This is configured but doesn't quite seem to be working in terms of logs, etc.?
- Configure Azure secrets
	- This is configured but doesn't work with private link for some reason.

Done:
x There is a white margin next to the Band Gig Schedule navbar (and rest of pages) that is unnecessary. 
x Fix expired access token not redirecting to login
x File logging isn't working
x- Remove venue address from reminder (as Waze, etc. will give notifications about leaving for the reminder)
x- Add ability to delete and recreate reminders
x- Make "Leave for" reminders into "Drive to" and set end time to expected arrival time.
x- Check for outdated reminders i.e. gig event has been updated since reminder created or gig event no longer exists.
x- Attach gigEventId as extendedProperty on Google calendar events.
x- Optimize queries.
- Color theme revamp?

x- Calendar sanity check page
x	- Find events that don't match expected "Gig-" or "NA" formats.
x	- Find gigs without location.
x	- Fing gigs without time. 

Frontend:
x- More phone friendly formatting
x- General cleanup/tweaks
x - Venue map needs optimized. Seems to be making lots of extra calls? Fails occasionally with weird API key issues?
x- Events list flashes back and forth between full list and filtered list when applying filter (before settling on correct state). 
x- Feature to idenity and clean up old reminders
	1. On backend, mark any reminders that appear in the search as isOld if they are for a date before today. 
		- This should use the existing queries, entities, etc. and be returned from GET /api/reminders along with any other reminders.
	2. On the frontend, the Reminders page should include this along with the existing reminders.
		- The Delete button should be on each of these reminders.
		- There should be clear labeling to indicate these are old reminders ("Date has passed").
		- Styling should have the whole reminder row grayed out (other than the Delete button).
	3. On the frontend, the Reminders page should have a 'Delete Outdated' button.
		- This button will delete all reminders that are marked as old AND all reminders currently marked with isOutdated.
		- This should be a single API call to the backend (see next) with the frontend passing the list of IDs to delete.
	4. On the backend, there should be a more flexible delete reminders endpoint.
		- We want the ability to support deleting the above classes of old/outdated reminders. Let's do this by adding an endpoint
		  that takes a list of reminder IDs to delete.
- Refactor implementation
	- Rename "Simulate" to something else
	- Make "Simulate" do all the work and Create just create from ReminderInfos
		- See the start/end time determination for an example where Create is doing more than it should.

----------------------------------------------
Prompts
----------------------------------------------

Mobile: Top title bar and bottom nav bar tweaks:
Currently the top has just the app icon and title as content, centered.
Let us make more efficient use of this space.
1. Add a pancake menu to the left. Move "Logout" here.
2. Add a filter menu to the right when a specific page has filter functionality. Currently this filter functionality is inline at the top of these pages, but instead should be shown 
dynamically when the filter button is tapped. 
3. Remove the pancake menu from the bottom navbar, replacing it with the Analytics tab. 
4. Remove the Sanity Checks page and any backing code that is only used by it. 



Opening:
Today we will be working on <@ the project or solution>. Please use Powershell for any terminal commands.

Done:
I would like to add a new section to the BandCalendar web site that performs a sanity check on the gig events calendar. 
This calendar is maintained by a number of humans and humans can often be incomplete or inconsistent, so we need to ensure events are all consistent and complete.
The calendar has two types of events -- gig events that are typically prefixed with "Gig-" and band member availability events that typically includes band member name and some variation of "NA"/"N/A"/"Not Available".
On the backend, we have EventType that marks events as either Gig, Availability, or Unknown events with EventClassifier containing the classification code.  
I would like to see a chronological list of events on the Sanity Check page that are flagged for the following:
- Don't match expected "Gig-" or "NA" formats (i.e. EventType is Unknown).
- Gig events without location.
- Gig events without start time and end time. 
 
Analytics Improvements:
Today we will be working on the Analytics page. 
- Please remove the "Gig Analytics for 2025" title.
- Make the year a dropdown from 2024 to 2026 with 2025 selected by default.
- Remove the "Update" button and have it automatically updated on dropdown change.
- Align the Year label with the dropdown to optimize vertical space.
- Put the "Venue Breakdown" section under a tabbed container (with "Venue Breakdown" being the tab title). 
- Add a tab for "Month Breakdown" that sums up totals by month. 
- Improve the venue title for mobile display. Currently the titles are very long as they include full address, so 
  they wrap awkwardly on smaller screens. 

